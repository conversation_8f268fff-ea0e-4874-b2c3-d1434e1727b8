<?php

/**
 * API V2 - Główny punkt wejścia
 * Wszystkie endpointy API v2 są obsługiwane przez ten plik
 */

// Ustawienia PHP
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Nagłówki CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// Obsługa OPTIONS (preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Załaduj konfigurację
if (!defined('KTOOSTATNI_APP')) {
    define('KTOOSTATNI_APP', true);
}
require_once __DIR__ . '/../../config.php';

// Autoloader dla klas API v2
spl_autoload_register(function ($class) {
    $directories = [
        __DIR__ . '/core/',
        __DIR__ . '/controllers/',
        __DIR__ . '/../core/', // Fallback do starego core
        __DIR__ . '/../../admin/core/', // Fallback do admin core
        __DIR__ . '/../../admin/models/' // Fallback do admin models
    ];

    foreach ($directories as $directory) {
        $file = $directory . $class . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

try {
    // Inicjalizacja bazy danych - UWAGA: Metoda init() nie istnieje
    // Database jest inicjalizowana automatycznie przez getInstance()

    // Usuń prefix /api/v2 z ścieżki jeśli istnieje (dla PHP built-in server)
    $requestUri = $_SERVER['REQUEST_URI'];
    $requestPath = parse_url($requestUri, PHP_URL_PATH);

    if (strpos($requestPath, '/api/v2') === 0) {
        // Usuń prefix /api/v2
        $cleanPath = substr($requestPath, 7); // Usuń '/api/v2'
        $cleanPath = $cleanPath ?: '/';

        // Zachowaj query string jeśli istnieje
        $queryString = parse_url($requestUri, PHP_URL_QUERY);
        $newUri = $cleanPath . ($queryString ? '?' . $queryString : '');

        $_SERVER['REQUEST_URI'] = $newUri;
    }

    // Debug routing
    if (isset($_GET['debug'])) {
        error_log("V2 API - REQUEST_URI: " . $_SERVER['REQUEST_URI']);
        error_log("V2 API - REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD']);
    }

    // Inicjalizacja routera
    $router = new ApiRouter();

    // === ENDPOINTY V2 API ===

    // Test endpoint
    $router->get('/test', function () {
        echo json_encode(['success' => true, 'message' => 'V2 API działa!']);
    });

    // Import danych z Chrome Extension
    $router->post('/import', [V2ApiController::class, 'importData']);

    // Wizyty dla PWA
    $router->get('/appointments/{doctorId}', [V2ApiController::class, 'getAppointments']);
    $router->post('/appointments/{appointmentId}/update', [V2ApiController::class, 'updateAppointment']);
    $router->post('/appointments/{appointmentId}/call', [V2ApiController::class, 'callAppointment']);

    // Kolejka dla wyświetlaczy
    $router->get('/queue/{clientId}', [V2ApiController::class, 'getQueueStatus']);

    // Sprawdzanie zmian
    $router->get('/changes/{clientId}', [V2ApiController::class, 'checkChanges']);

    // === ENDPOINTY CACHE ===

    // Pobranie wszystkich timestampów
    $router->get('/cache/timestamps', [CacheController::class, 'getTimestamps']);

    // Oznaczanie zmian
    $router->post('/cache/mark/{entityType}', [CacheController::class, 'markChanges']);

    // Statystyki cache
    $router->get('/cache/stats', [CacheController::class, 'getStats']);

    // === ENDPOINTY LEKARZA (PWA) ===

    // Logowanie lekarza
    $router->post('/doctor/login', [DoctorApiController::class, 'login']);

    // Wizyty lekarza
    $router->get('/doctor/appointments/{roomId}', [DoctorApiController::class, 'appointments']);

    // Operacje na wizytach
    $router->post('/doctor/call-next/{roomId}', [DoctorApiController::class, 'callNext']);
    $router->post('/doctor/previous/{roomId}', [DoctorApiController::class, 'previous']);
    $router->post('/doctor/skip-current/{roomId}', [DoctorApiController::class, 'skipCurrent']);
    $router->post('/doctor/call-specific/{roomId}', [DoctorApiController::class, 'callSpecificAppointment']);

    // Nawigacja między wizytami
    $router->post('/doctor/navigate-previous/{roomId}', [DoctorApiController::class, 'navigatePrevious']);
    $router->post('/doctor/navigate-next/{roomId}', [DoctorApiController::class, 'navigateNext']);

    // Sprawdzanie dostępności gabinetu
    $router->post('/doctor/check-room-availability', [DoctorApiController::class, 'checkRoomAvailability']);

    // Statystyki
    $router->get('/doctor/stats/{roomId}', [DoctorApiController::class, 'stats']);

    // === ENDPOINTY WYŚWIETLACZY ===

    // Dane dla wyświetlaczy
    $router->get('/display/{code}', [DisplayApiController::class, 'getDisplayData']);
    $router->post('/display/{code}/heartbeat', [DisplayApiController::class, 'heartbeat']);

    // === ENDPOINTY REKLAM ===

    // Dane reklam
    $router->get('/ads/{clientId}', [AdsApiController::class, 'getAds']);
    $router->post('/ads/view', [AdsApiController::class, 'recordView']);

    // === OBSŁUGA ROUTINGU ===

    $router->run();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Błąd serwera: ' . $e->getMessage(),
        'timestamp' => date('c')
    ]);
}
