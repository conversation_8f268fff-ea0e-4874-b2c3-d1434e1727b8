<?php

class AdsApiController extends ApiController {

    /**
     * Pobierz reklamy dla klienta
     */
    public function getAds($clientId) {
        try {
            // Pobierz aktywne kampanie dla klienta - UWAGA: tabele ads_assignments i ads_views zostały usunięte
            // System reklamowy został uproszczony - zwracamy bezpośrednio z tabeli ads
            $stmt = $this->db->prepare("
                SELECT c.id, c.name, c.media_type, c.media_url, c.youtube_id,
                       c.duration, 0.0001 as rate_per_second, c.description -- UWAGA: Kolumna rate_per_second została usunięta z ads
                FROM ads c
                WHERE 1=1 -- UWAGA: Kolumny is_active, budget, spent zostały usunięte z ads
                ORDER BY RANDOM()
                LIMIT 1
            ");

            $stmt->execute();
            $campaign = $stmt->fetch();

            if (!$campaign) {
                $this->success([
                    'ads' => [],
                    'message' => 'No ads available' // UWAGA: Zmieniono z campaigns na ads
                ]);
                return;
            }

            // System reklamowy uproszczony - bez sprawdzania częstotliwości
            // (tabela ads_views została usunięta)

            // Przygotuj URL mediów
            if ($campaign['media_type'] === 'image' || $campaign['media_type'] === 'video') {
                $campaign['media_url'] = $this->getFullMediaUrl($campaign['media_url']);
            }

            $this->success([
                'ads' => [$campaign],
                'client_id' => $clientId,
                'timestamp' => date('c')
            ]);
        } catch (Exception $e) {
            $this->log('Error getting ads: ' . $e->getMessage(), 'ERROR');
            $this->error('Failed to get ads', 500);
        }
    }

    /**
     * Zapisz wyświetlenie reklamy - UWAGA: Funkcjonalność wyłączona
     * Tabele ads_assignments i ads_views zostały usunięte
     */
    public function recordView() {
        try {
            $data = $this->getRequestData();
            $this->validateRequired($data, ['campaign_id', 'client_id', 'duration']);

            $campaignId = $data['campaign_id'];
            $clientId = $data['client_id'];
            $duration = (int)$data['duration'];

            // Sprawdź czy kampania istnieje i jest aktywna
            $stmt = $this->db->prepare("
                SELECT c.*
                FROM ads c
                WHERE c.id = ? -- UWAGA: Kolumna is_active została usunięta z ads
            ");
            $stmt->execute([$campaignId]);
            $campaign = $stmt->fetch();

            if (!$campaign) {
                $this->error('Campaign not found', 404);
            }

            // System reklamowy uproszczony - bez zapisywania wyświetleń i rozliczeń
            // Tabele ads_views i kolumny balance w users zostały usunięte

            // Oblicz koszt dla informacji - UWAGA: Kolumna rate_per_second została usunięta z ads
            $cost = $duration * 0.0001; // Stała wartość

            // UWAGA: Kolumny spent i budget zostały usunięte z ads - pomijamy sprawdzenie budżetu
            // if ($campaign['spent'] + $cost > $campaign['budget']) {
            //     $this->error('Campaign budget exceeded', 400);
            // }

            // Zaktualizuj tylko wydane środki w kampanii
            $stmt = $this->db->prepare("
                UPDATE ads
                SET spent = spent + ?
                WHERE id = ?
            ");
            $stmt->execute([$cost, $campaignId]);

            $this->success([
                'view_recorded' => true,
                'cost' => $cost,
                'duration' => $duration,
                'note' => 'Billing system disabled - ads_views table removed'
            ], 'View recorded successfully (simplified)');
        } catch (Exception $e) {
            $this->log('Error recording view: ' . $e->getMessage(), 'ERROR');
            $this->error('Failed to record view', 500);
        }
    }

    /**
     * Pobierz pełny URL do pliku multimedialnego
     */
    private function getFullMediaUrl($mediaUrl) {
        if (strpos($mediaUrl, 'http') === 0) {
            return $mediaUrl; // Już pełny URL
        }

        // Względny URL - dodaj bazowy URL (uproszczone bez Config)
        return '/' . ltrim($mediaUrl, '/');
    }
}
