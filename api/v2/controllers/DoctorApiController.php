<?php

class DoctorApiController extends ApiController {

    /**
     * Logowanie lekarza przez kod dostępu
     */
    public function login() {
        try {
            // Zapisz log do pliku
            file_put_contents(__DIR__ . '/../../debug.log', date('Y-m-d H:i:s') . " - DoctorAPI login wywołane\n", FILE_APPEND);

            $data = $this->getRequestData();
            file_put_contents(__DIR__ . '/../../debug.log', date('Y-m-d H:i:s') . " - Otr<PERSON><PERSON>e dane: " . json_encode($data) . "\n", FILE_APPEND);

            $this->validateRequired($data, ['access_code']);

            $accessCode = $data['access_code'];
            file_put_contents(__DIR__ . '/../../debug.log', date('Y-m-d H:i:s') . " - Kod dostępu: " . $accessCode . "\n", FILE_APPEND);

            if (strlen($accessCode) !== 12) {
                $this->error('Nieprawidłowy kod dostępu', 400);
            }

            // Znajdź lekarza po kodzie dostępu
            // UWAGA: Kolumny company_name i is_active zostały usunięte z users
            $stmt = $this->db->prepare("
                SELECT d.*, u.username as client_name, u.id as client_id
                FROM queue_doctors d
                JOIN users u ON 1=1 -- UWAGA: Kolumna client_id została usunięta z queue_doctors
                WHERE d.access_code = ? AND d.active = 1
            ");
            $stmt->execute([$accessCode]);
            $doctor = $stmt->fetch();

            error_log('DoctorAPI login - znaleziony lekarz: ' . json_encode($doctor));

            if (!$doctor) {
                error_log('DoctorAPI login - nie znaleziono lekarza dla kodu: ' . $accessCode);
                $this->error('Nieprawidłowy kod dostępu lub lekarz nieaktywny', 401);
            }

            // Przygotuj dane lekarza
            $photoUrl = $doctor['photo_url'];
            // Upewnij się że ścieżka do zdjęcia zaczyna się od /uploads/
            if ($photoUrl && !str_starts_with($photoUrl, '/uploads/')) {
                // Jeśli ścieżka zaczyna się od uploads/ (bez leading slash), dodaj slash na początku
                if (str_starts_with($photoUrl, 'uploads/')) {
                    $photoUrl = '/' . $photoUrl;
                }
                // Jeśli ścieżka nie zawiera uploads/ w ogóle, dodaj pełną ścieżkę
                else if (!str_contains($photoUrl, 'uploads/')) {
                    $photoUrl = '/uploads/' . $photoUrl;
                }
            }

            $doctorData = [
                'id' => $doctor['id'],
                'first_name' => $doctor['first_name'],
                'last_name' => $doctor['last_name'],
                'specialization' => $doctor['specialization'],
                'photo_url' => $photoUrl,
                'client_id' => 1, // UWAGA: Kolumna client_id została usunięta z queue_doctors
                'client_name' => $doctor['client_name']
            ];

            // Pobierz domyślny gabinet
            $defaultRoom = null;
            if ($doctor['default_room_id']) {
                $stmt = $this->db->prepare("
                    SELECT id, name
                    FROM queue_rooms
                    WHERE id = ?
                ");
                $stmt->execute([$doctor['default_room_id']]);
                $defaultRoom = $stmt->fetch();
            }

            // Pobierz wszystkie dostępne gabinety
            // UWAGA: Kolumny room_number, active, client_id zostały usunięte z queue_rooms
            $stmt = $this->db->prepare("
                SELECT r.id, r.name
                FROM queue_rooms r
                ORDER BY r.name
            ");
            $stmt->execute();
            $availableRooms = $stmt->fetchAll();

            $this->success([
                'doctor' => $doctorData,
                'default_room' => $defaultRoom,
                'available_rooms' => $availableRooms
            ]);
        } catch (Exception $e) {
            $this->error('Błąd logowania: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Pobierz wizyty dla lekarza w gabinecie
     */
    public function appointments($roomId) {
        try {
            $doctorId = $_GET['doctor_id'] ?? null;
            $date = $_GET['date'] ?? date('Y-m-d'); // Domyślnie dzisiejsza data

            if (!$doctorId) {
                $this->error('Brak ID lekarza', 400);
            }

            // Sprawdź czy gabinet istnieje
            // UWAGA: Kolumny client_id, active zostały usunięte z queue_rooms
            $stmt = $this->db->prepare("
                SELECT r.*
                FROM queue_rooms r
                WHERE r.id = ?
            ");
            $stmt->execute([$roomId]);
            $room = $stmt->fetch();

            if (!$room) {
                $this->error('Gabinet nie istnieje lub nieaktywny', 404);
            }

            // Pobierz aktualną wizytę dla lekarza (niezależnie od gabinetu)
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE doctor_id = ? AND appointment_date = ? AND status = 'current'
                ORDER BY appointment_time ASC
                LIMIT 1
            ");
            $stmt->execute([$doctorId, $date]);
            $current = $stmt->fetch();

            // Pobierz oczekujące wizyty dla lekarza (niezależnie od gabinetu)
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE doctor_id = ? AND appointment_date = ? AND status = 'waiting'
                ORDER BY appointment_time ASC
                LIMIT 10
            ");
            $stmt->execute([$doctorId, $date]);
            $waiting = $stmt->fetchAll();

            // Pobierz wszystkie wizyty dla PWA (do wyświetlenia pełnej listy)
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE doctor_id = ? AND appointment_date = ?
                ORDER BY appointment_time ASC
            ");
            $stmt->execute([$doctorId, $date]);
            $allAppointments = $stmt->fetchAll();

            $this->success([
                'room' => $room,
                'current' => $current,
                'waiting' => $waiting,
                'all_appointments' => $allAppointments
            ]);
        } catch (Exception $e) {
            $this->error('Błąd pobierania wizyt: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Wywołaj następną wizytę
     */
    public function callNext($roomId) {
        try {
            $data = $this->getRequestData();
            $this->validateRequired($data, ['doctor_id']);

            $doctorId = $data['doctor_id'];

            // Rozpocznij transakcję
            $this->db->beginTransaction();

            // Zakończ aktualną wizytę lekarza
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'closed', completed_at = datetime('now', 'localtime')
                WHERE doctor_id = ? AND status = 'current'
            ");
            $stmt->execute([$doctorId]);

            // Wywołaj następną wizytę lekarza
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'current', called_at = datetime('now', 'localtime'), is_patient_present = 2
                WHERE id = (
                    SELECT id FROM queue_appointments
                    WHERE doctor_id = ? AND status = 'waiting'
                    ORDER BY appointment_time ASC
                    LIMIT 1
                )
            ");
            $stmt->execute([$doctorId]);

            // Pobierz nową aktualną wizytę
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE doctor_id = ? AND status = 'current'
            ");
            $stmt->execute([$doctorId]);
            $nextAppointment = $stmt->fetch();

            $this->db->commit();

            if ($nextAppointment) {
                $this->success(['appointment' => $nextAppointment]);
            } else {
                $this->success(['appointment' => null, 'message' => 'Brak kolejnych wizyt']);
            }
        } catch (Exception $e) {
            $this->db->rollBack();
            $this->error('Błąd wywołania następnej wizyty: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Wróć do poprzedniej wizyty
     */
    public function previous($roomId) {
        try {
            $data = $this->getRequestData();
            $this->validateRequired($data, ['doctor_id']);

            $doctorId = $data['doctor_id'];

            // Rozpocznij transakcję
            $this->db->beginTransaction();

            // Znajdź ostatnią zakończoną wizytę lekarza
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE doctor_id = ? AND status = 'closed'
                ORDER BY completed_at DESC
                LIMIT 1
            ");
            $stmt->execute([$doctorId]);
            $lastCompleted = $stmt->fetch();

            if (!$lastCompleted) {
                $this->db->rollBack();
                $this->error('Brak poprzedniej wizyty do przywrócenia', 404);
            }

            // Ustaw aktualną wizytę jako oczekującą
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'waiting', called_at = NULL
                WHERE doctor_id = ? AND status = 'current'
            ");
            $stmt->execute([$doctorId]);

            // Przywróć poprzednią wizytę jako aktualną
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'current', completed_at = NULL, called_at = datetime('now', 'localtime')
                WHERE id = ?
            ");
            $stmt->execute([$lastCompleted['id']]);

            $this->db->commit();

            $this->success(['appointment' => $lastCompleted]);
        } catch (Exception $e) {
            $this->db->rollBack();
            $this->error('Błąd cofania wizyty: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Pomiń aktualną wizytę
     */
    public function skipCurrent($roomId) {
        try {
            $data = $this->getRequestData();
            $this->validateRequired($data, ['doctor_id']);

            $doctorId = $data['doctor_id'];

            // Rozpocznij transakcję
            $this->db->beginTransaction();

            // Oznacz aktualną wizytę lekarza jako pominiętą
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'closed', completed_at = datetime('now', 'localtime')
                WHERE doctor_id = ? AND status = 'current'
            ");
            $stmt->execute([$doctorId]);

            // Wywołaj następną wizytę lekarza
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'current', called_at = datetime('now', 'localtime')
                WHERE id = (
                    SELECT id FROM queue_appointments
                    WHERE doctor_id = ? AND status = 'waiting'
                    ORDER BY appointment_time ASC
                    LIMIT 1
                )
            ");
            $stmt->execute([$doctorId]);

            // Pobierz nową aktualną wizytę
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE doctor_id = ? AND status = 'current'
            ");
            $stmt->execute([$doctorId]);
            $nextAppointment = $stmt->fetch();

            $this->db->commit();

            if ($nextAppointment) {
                $this->success(['appointment' => $nextAppointment]);
            } else {
                $this->success(['appointment' => null, 'message' => 'Brak kolejnych wizyt']);
            }
        } catch (Exception $e) {
            $this->db->rollBack();
            $this->error('Błąd pomijania wizyty: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Sprawdź dostępność gabinetu dla lekarza w danym dniu
     */
    public function checkRoomAvailability() {
        try {
            $data = $this->getRequestData();
            $this->validateRequired($data, ['doctor_id', 'room_id', 'date']);

            $doctorId = $data['doctor_id'];
            $roomId = $data['room_id'];
            $date = $data['date'];

            // Sprawdź czy lekarz istnieje i jest aktywny
            // UWAGA: Kolumny company_name i is_active zostały usunięte z users
            $stmt = $this->db->prepare("
                SELECT d.*, u.username as client_name
                FROM queue_doctors d
                JOIN users u ON d.client_id = u.id
                WHERE d.id = ? AND d.active = 1
            ");
            $stmt->execute([$doctorId]);
            $doctor = $stmt->fetch();

            if (!$doctor) {
                $this->error('Lekarz nie istnieje lub nieaktywny', 404);
            }

            // Sprawdź czy gabinet istnieje
            // UWAGA: Kolumny client_id, active zostały usunięte z queue_rooms
            $stmt = $this->db->prepare("
                SELECT r.*
                FROM queue_rooms r
                WHERE r.id = ?
            ");
            $stmt->execute([$roomId]);
            $room = $stmt->fetch();

            if (!$room) {
                $this->error('Gabinet nie istnieje', 404);
            }

            $this->success([
                'available' => true,
                'room' => $room,
                'doctor' => $doctor,
                'date' => $date
            ]);
        } catch (Exception $e) {
            $this->error('Błąd sprawdzania dostępności gabinetu: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Nawiguj do poprzedniej wizyty (bez kończenia aktualnej)
     */
    public function navigatePrevious($roomId) {
        try {
            $data = $this->getRequestData();
            $this->validateRequired($data, ['doctor_id']);

            $doctorId = $data['doctor_id'];
            $date = $data['date'] ?? date('Y-m-d');

            // Rozpocznij transakcję
            $this->db->beginTransaction();

            // Pobierz aktualną wizytę
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE doctor_id = ? AND appointment_date = ? AND status = 'current'
                LIMIT 1
            ");
            $stmt->execute([$doctorId, $date]);
            $currentAppointment = $stmt->fetch();

            if (!$currentAppointment) {
                $this->db->rollBack();
                $this->error('Brak aktualnej wizyty', 404);
            }

            // Znajdź poprzednią wizytę (wcześniejszą godzinę)
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE doctor_id = ? AND appointment_date = ?
                AND appointment_time < ?
                AND status IN ('waiting', 'closed')
                ORDER BY appointment_time DESC
                LIMIT 1
            ");
            $stmt->execute([$doctorId, $date, $currentAppointment['appointment_time']]);
            $previousAppointment = $stmt->fetch();

            if (!$previousAppointment) {
                $this->db->rollBack();
                $this->error('Brak poprzedniej wizyty', 404);
            }

            // Ustaw aktualną wizytę jako oczekującą
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'waiting', called_at = NULL
                WHERE id = ?
            ");
            $stmt->execute([$currentAppointment['id']]);

            // Ustaw poprzednią wizytę jako aktualną
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'current', called_at = datetime('now', 'localtime'), completed_at = NULL, is_patient_present = 2
                WHERE id = ?
            ");
            $stmt->execute([$previousAppointment['id']]);

            $this->db->commit();

            $this->success(['appointment' => $previousAppointment]);
        } catch (Exception $e) {
            $this->db->rollBack();
            $this->error('Błąd nawigacji do poprzedniej wizyty: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Nawiguj do następnej wizyty (bez kończenia aktualnej)
     */
    public function navigateNext($roomId) {
        try {
            $data = $this->getRequestData();
            $this->validateRequired($data, ['doctor_id']);

            $doctorId = $data['doctor_id'];
            $date = $data['date'] ?? date('Y-m-d');

            // Rozpocznij transakcję
            $this->db->beginTransaction();

            // Pobierz aktualną wizytę
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE doctor_id = ? AND appointment_date = ? AND status = 'current'
                LIMIT 1
            ");
            $stmt->execute([$doctorId, $date]);
            $currentAppointment = $stmt->fetch();

            if (!$currentAppointment) {
                $this->db->rollBack();
                $this->error('Brak aktualnej wizyty', 404);
            }

            // Znajdź następną wizytę (późniejszą godzinę)
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE doctor_id = ? AND appointment_date = ?
                AND appointment_time > ?
                AND status IN ('waiting', 'closed')
                ORDER BY appointment_time ASC
                LIMIT 1
            ");
            $stmt->execute([$doctorId, $date, $currentAppointment['appointment_time']]);
            $nextAppointment = $stmt->fetch();

            if (!$nextAppointment) {
                $this->db->rollBack();
                $this->error('Brak następnej wizyty', 404);
            }

            // Ustaw aktualną wizytę jako oczekującą
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'waiting', called_at = NULL
                WHERE id = ?
            ");
            $stmt->execute([$currentAppointment['id']]);

            // Ustaw następną wizytę jako aktualną
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'current', called_at = datetime('now', 'localtime'), completed_at = NULL, is_patient_present = 2
                WHERE id = ?
            ");
            $stmt->execute([$nextAppointment['id']]);

            $this->db->commit();

            $this->success(['appointment' => $nextAppointment]);
        } catch (Exception $e) {
            $this->db->rollBack();
            $this->error('Błąd nawigacji do następnej wizyty: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Wywołaj konkretną wizytę (dowolnego pacjenta)
     */
    public function callSpecificAppointment($roomId) {
        try {
            $data = $this->getRequestData();
            $this->validateRequired($data, ['doctor_id', 'appointment_id']);

            $doctorId = $data['doctor_id'];
            $appointmentId = $data['appointment_id'];

            // Rozpocznij transakcję
            $this->db->beginTransaction();

            // Sprawdź czy wizyta istnieje i należy do tego lekarza
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE id = ? AND doctor_id = ?
            ");
            $stmt->execute([$appointmentId, $doctorId]);
            $appointment = $stmt->fetch();

            if (!$appointment) {
                $this->db->rollBack();
                $this->error('Wizyta nie została znaleziona lub nie należy do tego lekarza', 404);
            }

            // Zakończ aktualną wizytę lekarza (jeśli istnieje)
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'closed', completed_at = datetime('now', 'localtime')
                WHERE doctor_id = ? AND status = 'current'
            ");
            $stmt->execute([$doctorId]);

            // Wywołaj konkretną wizytę
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'current', called_at = datetime('now', 'localtime'), is_patient_present = 2
                WHERE id = ?
            ");
            $stmt->execute([$appointmentId]);

            $this->db->commit();

            $this->success([
                'appointment' => $appointment,
                'message' => 'Wizyta została wywołana'
            ]);
        } catch (Exception $e) {
            $this->db->rollBack();
            $this->error('Błąd wywołania konkretnej wizyty: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Pobierz statystyki dla gabinetu
     */
    public function stats($roomId) {
        try {
            $doctorId = $_GET['doctor_id'] ?? null;
            $date = $_GET['date'] ?? date('Y-m-d');

            if (!$doctorId) {
                $this->error('Brak ID lekarza', 400);
            }

            // Sprawdź czy gabinet istnieje
            // UWAGA: Kolumny client_id, active zostały usunięte z queue_rooms
            $stmt = $this->db->prepare("
                SELECT r.*
                FROM queue_rooms r
                WHERE r.id = ?
            ");
            $stmt->execute([$roomId]);
            $room = $stmt->fetch();

            if (!$room) {
                $this->error('Gabinet nie istnieje lub nieaktywny', 404);
            }

            // Pobierz statystyki wizyt lekarza dla danego dnia
            $stmt = $this->db->prepare("
                SELECT
                    COUNT(*) as total_appointments,
                    SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as completed_appointments,
                    SUM(CASE WHEN status = 'waiting' THEN 1 ELSE 0 END) as waiting_appointments,
                    SUM(CASE WHEN status = 'current' THEN 1 ELSE 0 END) as current_appointments
                FROM queue_appointments
                WHERE doctor_id = ? AND appointment_date = ?
            ");
            $stmt->execute([$doctorId, $date]);
            $stats = $stmt->fetch();

            $this->success([
                'room' => $room,
                'date' => $date,
                'stats' => $stats
            ]);
        } catch (Exception $e) {
            $this->error('Błąd pobierania statystyk: ' . $e->getMessage(), 500);
        }
    }
}
