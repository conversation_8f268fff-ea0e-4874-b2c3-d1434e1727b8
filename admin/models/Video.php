<?php

/**
 * Model Video - zarządzanie materiałami video
 */
class Video {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Pobierz wszystkie materiały video
     */
    public function getAllVideos() {
        $sql = "
            SELECT
                a.*,
                ac.name as category_name,
                u.username as advertiser_name,
                aa.name as ads_advertiser_name
            FROM ads a
            LEFT JOIN ads_categories ac ON a.category_id = ac.id
            LEFT JOIN users u ON a.advertiser_id = u.id
            LEFT JOIN ads_advertiser aa ON a.ads_advertiser_id = aa.id
            WHERE a.media_type = 'video'
            ORDER BY a.created_at DESC
        ";

        $videos = $this->db->fetchAll($sql);

        // Dodaj informacje o auto-akceptacji z settings
        foreach ($videos as &$video) {
            if ($video['category_id']) {
                $settingKey = "video_category_auto_accept_{$video['category_id']}";
                $autoAccept = $this->db->fetchOne(
                    "SELECT value FROM settings WHERE key = ?",
                    [$settingKey]
                );
                $video['category_auto_accept'] = $autoAccept ? (int)$autoAccept['value'] : 0;
            } else {
                $video['category_auto_accept'] = 0;
            }
        }

        return $videos;
    }

    /**
     * Pobierz materiały video według statusu
     */
    public function getVideosByStatus($status) {
        $sql = "
            SELECT
                a.*,
                vc.name as category_name,
                vc.auto_accept as category_auto_accept,
                u.username as advertiser_name
            FROM ads a
            LEFT JOIN video_categories vc ON a.video_category_id = vc.id
            LEFT JOIN users u ON a.advertiser_id = u.id
            WHERE a.media_type = 'video' AND a.approval_status = ?
            ORDER BY a.created_at DESC
        ";

        return $this->db->fetchAll($sql, [$status]);
    }

    /**
     * Pobierz materiały video według kategorii
     */
    public function getVideosByCategory($categoryId) {
        $sql = "
            SELECT
                a.*,
                ac.name as category_name,
                u.username as advertiser_name,
                aa.name as ads_advertiser_name
            FROM ads a
            LEFT JOIN ads_categories ac ON a.category_id = ac.id
            LEFT JOIN users u ON a.advertiser_id = u.id
            LEFT JOIN ads_advertiser aa ON a.ads_advertiser_id = aa.id
            WHERE a.media_type = 'video' AND a.category_id = ?
            ORDER BY a.created_at DESC
        ";

        $videos = $this->db->fetchAll($sql, [$categoryId]);

        // Dodaj informacje o auto-akceptacji z settings
        foreach ($videos as &$video) {
            $settingKey = "video_category_auto_accept_{$categoryId}";
            $autoAccept = $this->db->fetchOne(
                "SELECT value FROM settings WHERE key = ?",
                [$settingKey]
            );
            $video['category_auto_accept'] = $autoAccept ? (int)$autoAccept['value'] : 0;
        }

        return $videos;
    }

    /**
     * Pobierz materiał video po ID
     */
    public function getById($id) {
        $sql = "
            SELECT
                a.*,
                ac.name as category_name,
                u.username as advertiser_name,
                aa.name as ads_advertiser_name
            FROM ads a
            LEFT JOIN ads_categories ac ON a.category_id = ac.id
            LEFT JOIN users u ON a.advertiser_id = u.id
            LEFT JOIN ads_advertiser aa ON a.ads_advertiser_id = aa.id
            WHERE a.id = ? AND a.media_type = 'video'
        ";

        $video = $this->db->fetchOne($sql, [$id]);

        // Dodaj informacje o auto-akceptacji z settings
        if ($video && $video['category_id']) {
            $settingKey = "video_category_auto_accept_{$video['category_id']}";
            $autoAccept = $this->db->fetchOne(
                "SELECT value FROM settings WHERE key = ?",
                [$settingKey]
            );
            $video['category_auto_accept'] = $autoAccept ? (int)$autoAccept['value'] : 0;
        } else {
            $video['category_auto_accept'] = 0;
        }

        return $video;
    }

    /**
     * Zaakceptuj materiał video
     */
    public function approve($id) {
        $sql = "UPDATE ads SET approval_status = 'approved' WHERE id = ? AND media_type = 'video'";
        return $this->db->execute($sql, [$id]);
    }

    /**
     * Odrzuć materiał video
     */
    public function reject($id) {
        $sql = "UPDATE ads SET approval_status = 'rejected' WHERE id = ? AND media_type = 'video'";
        return $this->db->execute($sql, [$id]);
    }

    /**
     * Ustaw status oczekiwania
     */
    public function setPending($id) {
        $sql = "UPDATE ads SET approval_status = 'pending' WHERE id = ? AND media_type = 'video'";
        return $this->db->execute($sql, [$id]);
    }

    /**
     * Sprawdź czy materiał video powinien być automatycznie zaakceptowany
     */
    public function shouldAutoApprove($categoryId) {
        if (!$categoryId) {
            return false;
        }

        $sql = "SELECT auto_accept FROM video_categories WHERE id = ?";
        $category = $this->db->fetchOne($sql, [$categoryId]);

        return $category && $category['auto_accept'] == 1;
    }

    /**
     * Automatycznie zaakceptuj materiały video z kategorii z auto_accept = 1
     */
    public function autoApproveByCategory() {
        $sql = "
            UPDATE ads
            SET approval_status = 'approved'
            WHERE media_type = 'video'
            AND approval_status = 'pending'
            AND video_category_id IN (
                SELECT id FROM video_categories WHERE auto_accept = 1
            )
        ";

        return $this->db->execute($sql);
    }

    /**
     * Zaakceptuj wszystkie materiały video w określonej kategorii
     */
    public function approveCategoryVideos($categoryId) {
        $sql = "
            UPDATE ads
            SET approval_status = 'approved'
            WHERE media_type = 'video'
            AND approval_status = 'pending'
            AND category_id = ?
        ";

        // Przygotuj i wykonaj zapytanie
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$categoryId]);

        // Zwróć liczbę zaktualizowanych rekordów
        return $stmt->rowCount();
    }

    /**
     * Pobierz statystyki materiałów video
     */
    public function getStats() {
        $sql = "
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN approval_status = 'approved' THEN 1 ELSE 0 END) as approved,
                SUM(CASE WHEN approval_status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN approval_status = 'rejected' THEN 1 ELSE 0 END) as rejected
            FROM ads 
            WHERE media_type = 'video'
        ";

        return $this->db->fetchOne($sql);
    }

    /**
     * Usuń materiał video
     */
    public function delete($id) {
        $sql = "DELETE FROM ads WHERE id = ? AND media_type = 'video'";
        return $this->db->execute($sql, [$id]);
    }

    /**
     * Zaktualizuj kategorię materiału video
     */
    public function updateCategory($id, $categoryId) {
        $sql = "UPDATE ads SET category_id = ? WHERE id = ? AND media_type = 'video'";
        return $this->db->execute($sql, [$categoryId, $id]);
    }



    /**
     * Zaktualizuj video
     */
    public function update($id, $data) {
        $setParts = [];
        $params = [];

        foreach ($data as $key => $value) {
            $setParts[] = "$key = ?";
            $params[] = $value;
        }

        $params[] = $id;

        $sql = "
            UPDATE ads
            SET " . implode(', ', $setParts) . "
            WHERE id = ?
        ";

        return $this->db->execute($sql, $params);
    }

    /**
     * Utwórz nowy materiał video
     */
    public function create($data) {
        $columns = [];
        $placeholders = [];
        $params = [];

        foreach ($data as $key => $value) {
            $columns[] = $key;
            $placeholders[] = '?';
            $params[] = $value;
        }

        $sql = "
            INSERT INTO ads (" . implode(', ', $columns) . ", created_at)
            VALUES (" . implode(', ', $placeholders) . ", datetime('now'))
        ";

        return $this->db->execute($sql, $params);
    }
}
