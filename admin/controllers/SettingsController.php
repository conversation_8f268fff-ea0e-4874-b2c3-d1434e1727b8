<?php

/**
 * SettingsController - zarządzanie ustawieniami systemu
 */
class SettingsController {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Panel ustawień
     */
    public function index() {
        $success = null;
        $error = null;

        // Obsługa formularza
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $action = $_POST['action'] ?? '';

            switch ($action) {
                case 'update_general':
                    $result = $this->updateGeneralSettings();
                    if ($result['success']) {
                        $success = $result['message'];
                    } else {
                        $error = $result['message'];
                    }
                    break;

                case 'update_queue':
                    $result = $this->updateQueueSettings();
                    if ($result['success']) {
                        $success = $result['message'];
                    } else {
                        $error = $result['message'];
                    }
                    break;

                case 'update_status_settings':
                    $result = $this->updateStatusSettings();
                    if ($result['success']) {
                        $success = $result['message'];
                    } else {
                        $error = $result['message'];
                    }
                    break;

                case 'clear_cache':
                    $result = $this->clearCache();
                    if ($result['success']) {
                        $success = $result['message'];
                    } else {
                        $error = $result['message'];
                    }
                    break;
            }
        }

        // Pobierz aktualne ustawienia
        $settings = $this->getCurrentSettings();

        $this->render('settings/index', [
            'settings' => $settings,
            'success' => $success,
            'error' => $error,
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Aktualizuj ustawienia ogólne
     */
    private function updateGeneralSettings() {
        try {
            // UWAGA: Kolumna company_name została usunięta z users
            // $companyName = trim($_POST['company_name'] ?? '');
            $defaultAppointmentDuration = (int)($_POST['default_appointment_duration'] ?? 15);
            $autoRefreshInterval = (int)($_POST['auto_refresh_interval'] ?? 30);

            if ($defaultAppointmentDuration < 5 || $defaultAppointmentDuration > 120) {
                return ['success' => false, 'message' => 'Czas wizyty musi być między 5 a 120 minut'];
            }

            if ($autoRefreshInterval < 10 || $autoRefreshInterval > 300) {
                return ['success' => false, 'message' => 'Interwał odświeżania musi być między 10 a 300 sekund'];
            }

            // Zapisz ustawienia do bazy danych
            $this->saveSetting('default_appointment_duration', $defaultAppointmentDuration);
            $this->saveSetting('auto_refresh_interval', $autoRefreshInterval);

            return ['success' => true, 'message' => 'Ustawienia ogólne zostały zaktualizowane'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Błąd podczas zapisywania ustawień: ' . $e->getMessage()];
        }
    }

    /**
     * Aktualizuj ustawienia kolejki
     */
    private function updateQueueSettings() {
        try {
            $maxWaitingTime = (int)($_POST['max_waiting_time'] ?? 60);
            $enableSmsNotifications = isset($_POST['enable_sms_notifications']) ? 1 : 0;
            $enableEmailNotifications = isset($_POST['enable_email_notifications']) ? 1 : 0;

            if ($maxWaitingTime < 15 || $maxWaitingTime > 480) {
                return ['success' => false, 'message' => 'Maksymalny czas oczekiwania musi być między 15 a 480 minut'];
            }

            // Zapisz ustawienia do bazy danych
            $this->saveSetting('max_waiting_time', $maxWaitingTime);
            $this->saveSetting('enable_sms_notifications', $enableSmsNotifications);
            $this->saveSetting('enable_email_notifications', $enableEmailNotifications);

            return ['success' => true, 'message' => 'Ustawienia kolejki zostały zaktualizowane'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Błąd podczas zapisywania ustawień kolejki: ' . $e->getMessage()];
        }
    }

    /**
     * Aktualizuj ustawienia statusów
     */
    private function updateStatusSettings() {
        try {
            $enableAppointmentConfirmation = isset($_POST['enable_appointment_confirmation']) ? 1 : 0;
            $enableAttendanceConfirmation = isset($_POST['enable_attendance_confirmation']) ? 1 : 0;
            $enableSmsSending = isset($_POST['enable_sms_sending']) ? 1 : 0;

            // Zapisz ustawienia statusów do bazy danych
            $this->saveSetting('enable_appointment_confirmation', $enableAppointmentConfirmation);
            $this->saveSetting('enable_attendance_confirmation', $enableAttendanceConfirmation);
            $this->saveSetting('enable_sms_sending', $enableSmsSending);

            return ['success' => true, 'message' => 'Ustawienia statusów zostały zaktualizowane'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Błąd podczas zapisywania ustawień statusów: ' . $e->getMessage()];
        }
    }

    /**
     * Zapisz ustawienie do bazy danych
     */
    private function saveSetting($key, $value) {
        // Sprawdź czy ustawienie już istnieje
        $existing = $this->db->fetchOne("SELECT key FROM settings WHERE key = ?", [$key]);

        if ($existing) {
            // Aktualizuj istniejące ustawienie
            $sql = "UPDATE settings SET value = ?, updated_at = datetime('now') WHERE key = ?";
        } else {
            // Wstaw nowe ustawienie
            $sql = "INSERT INTO settings (key, value, created_at, updated_at) VALUES (?, ?, datetime('now'), datetime('now'))";
        }

        return $this->db->execute($sql, [$value, $key]);
    }

    /**
     * Pobierz ustawienie z bazy danych
     */
    private function getSetting($key, $defaultValue = null) {
        $sql = "SELECT value FROM settings WHERE key = ?";
        $result = $this->db->fetchOne($sql, [$key]);

        return $result ? $result['value'] : $defaultValue;
    }

    /**
     * Wyczyść cache
     */
    private function clearCache() {
        try {
            // Tutaj można dodać logikę czyszczenia cache
            // Na przykład usunięcie plików tymczasowych, cache'u sesji itp.

            return ['success' => true, 'message' => 'Cache został wyczyszczony'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Błąd podczas czyszczenia cache: ' . $e->getMessage()];
        }
    }

    /**
     * Pobierz aktualne ustawienia
     */
    public function getCurrentSettings() {
        return [
            // 'company_name' => 'KtoOstatni', // UWAGA: Kolumna company_name została usunięta
            'default_appointment_duration' => (int)($this->getSetting('default_appointment_duration', 15)),
            'auto_refresh_interval' => (int)($this->getSetting('auto_refresh_interval', 30)),
            'max_waiting_time' => (int)($this->getSetting('max_waiting_time', 60)),
            'enable_sms_notifications' => (bool)($this->getSetting('enable_sms_notifications', 1)),
            'enable_email_notifications' => (bool)($this->getSetting('enable_email_notifications', 0)),
            'enable_appointment_confirmation' => (bool)($this->getSetting('enable_appointment_confirmation', 1)),
            'enable_attendance_confirmation' => (bool)($this->getSetting('enable_attendance_confirmation', 1)),
            'enable_sms_sending' => (bool)($this->getSetting('enable_sms_sending', 1)),
            'system_version' => '4.0',
            'database_size' => $this->getDatabaseSize(),
            'total_appointments' => $this->getTotalAppointments(),
            'total_doctors' => $this->getTotalDoctors()
        ];
    }

    /**
     * Pobierz rozmiar bazy danych
     */
    private function getDatabaseSize() {
        try {
            $dbPath = __DIR__ . '/../../db/reklama.db';
            if (file_exists($dbPath)) {
                $size = filesize($dbPath);
                return $this->formatBytes($size);
            }
            return 'Nieznany';
        } catch (Exception $e) {
            return 'Błąd';
        }
    }

    /**
     * Pobierz liczbę wizyt
     */
    private function getTotalAppointments() {
        try {
            return $this->db->fetchCount("SELECT COUNT(*) FROM queue_appointments");
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * Pobierz liczbę lekarzy
     */
    private function getTotalDoctors() {
        try {
            return $this->db->fetchCount("SELECT COUNT(*) FROM queue_doctors WHERE active = 1");
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * Formatuj bajty na czytelny format
     */
    private function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Renderuj widok
     */
    private function render($view, $data = []) {
        extract($data);

        ob_start();
        include ADMIN_PATH . '/views/' . $view . '.php';
        $content = ob_get_clean();

        include ADMIN_PATH . '/views/layout.php';
    }
}
