<?php

/**
 * DashboardController - główny panel zarządzania kolejkami
 */
class DashboardController {
    private $db;
    private $doctorModel;
    private $appointmentModel;
    private $displayModel;
    private $videoModel;

    public function __construct() {
        $this->db = Database::getInstance();
        $this->doctorModel = new Doctor();
        $this->appointmentModel = new Appointment();
        $this->displayModel = new Display();
        $this->videoModel = new Video();
    }

    /**
     * Główny panel dashboard
     */
    public function index() {
        // Pobierz wybraną datę z sesji lub użyj dzisiejszej
        $selectedDate = $_GET['date'] ?? $_SESSION['selected_date'] ?? date('Y-m-d');

        // Walidacja daty
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $selectedDate)) {
            $selectedDate = date('Y-m-d');
        }

        // Zapisz datę w sesji
        $_SESSION['selected_date'] = $selectedDate;

        // Pobierz lekarzy z wizytami na wybraną datę
        $doctors = $this->doctorModel->getDoctorsWithAppointments($selectedDate);

        // Pobierz statystyki
        $stats = $this->getStats($selectedDate);

        // Pobierz statystyki wyświetlaczy
        $displayStats = $this->getDisplayStats();

        // Pobierz statystyki reklam
        $adStats = $this->getAdStats();

        // Pobierz statystyki bazy danych
        $dbStats = $this->db->getStats();

        // Pobierz średnie opóźnienie
        $averageDelay = $this->appointmentModel->getAverageDelay($selectedDate);

        // Dla każdego lekarza pobierz szczegółowe informacje
        foreach ($doctors as &$doctor) {
            $doctor['current_appointment'] = $this->doctorModel->getCurrentAppointment($doctor['id'], $selectedDate);
            $doctor['waiting_appointments'] = $this->doctorModel->getWaitingAppointments($doctor['id'], $selectedDate, 50);
            $doctor['delay_minutes'] = $this->doctorModel->calculateAccurateDelay($doctor['id'], $selectedDate);
            $doctor['status'] = $this->doctorModel->getStatus($doctor['id'], $selectedDate);
            $doctor['photo_url'] = getDoctorPhotoUrl($doctor['photo_url']);
        }

        // Renderuj widok
        $this->render('dashboard', [
            'doctors' => $doctors,
            'stats' => $stats,
            'displayStats' => $displayStats,
            'adStats' => $adStats,
            'dbStats' => $dbStats,
            'selectedDate' => $selectedDate,
            'averageDelay' => $averageDelay,
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Pobierz statystyki dla dashboardu
     */
    private function getStats($date) {
        $stats = [];

        // Liczba lekarzy z wizytami - UWAGA: Kolumna client_id została usunięta z queue_appointments
        $stats['working_doctors'] = $this->db->fetchCount("
            SELECT COUNT(DISTINCT d.id)
            FROM queue_doctors d
            JOIN queue_appointments a ON d.id = a.doctor_id
            WHERE d.active = 1 AND a.appointment_date = ?
        ", [$date]);

        // Łączna liczba pacjentów
        $stats['total_patients'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM queue_appointments
            WHERE appointment_date = ?
        ", [$date]);

        // Statystyki wizyt według statusu
        $stats['waiting_patients'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM queue_appointments
            WHERE appointment_date = ? AND status = 'waiting'
        ", [$date]);

        $stats['current_patients'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM queue_appointments
            WHERE appointment_date = ? AND status = 'current'
        ", [$date]);

        $stats['completed_patients'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM queue_appointments
            WHERE appointment_date = ? AND status = 'completed'
        ", [$date]);

        // Łączna liczba lekarzy
        $stats['total_doctors'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM queue_doctors WHERE active = 1
        ");

        // Łączna liczba pokojów
        $stats['total_rooms'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM queue_rooms
        ");

        return $stats;
    }

    /**
     * Pobierz statystyki wyświetlaczy
     */
    private function getDisplayStats() {
        $stats = [];

        // Łączna liczba wyświetlaczy
        $stats['total'] = $this->db->fetchCount("SELECT COUNT(*) FROM client_displays");

        // Sparowane wyświetlacze
        $stats['paired'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM client_displays
            WHERE pairing_status = 'paired'
        ");

        // Online wyświetlacze
        $stats['online'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM client_displays
            WHERE is_online = 1 AND pairing_status = 'paired'
        ");

        // Oczekujące na parowanie
        $stats['pending'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM client_displays
            WHERE pairing_status = 'pending' AND pairing_expires_at > datetime('now')
        ");

        return $stats;
    }

    /**
     * Pobierz statystyki reklam
     */
    private function getAdStats() {
        $stats = [];

        // Łączna liczba reklam
        $stats['total'] = $this->db->fetchCount("SELECT COUNT(*) FROM ads");

        // Reklamy według typu mediów
        $stats['videos'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM ads WHERE media_type = 'video'
        ");

        $stats['images'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM ads WHERE media_type = 'image'
        ");

        $stats['youtube'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM ads WHERE media_type = 'youtube'
        ");

        // Reklamy według statusu zatwierdzenia
        $stats['approved'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM ads WHERE approval_status = 'approved'
        ");

        $stats['pending'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM ads WHERE approval_status = 'pending'
        ");

        $stats['rejected'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM ads WHERE approval_status = 'rejected'
        ");

        // Reklamy według aktywności
        $stats['active'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM ads WHERE is_active = 1
        ");

        $stats['inactive'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM ads WHERE is_active = 0
        ");

        // Liczba reklamodawców
        $stats['advertisers'] = $this->db->fetchCount("SELECT COUNT(*) FROM ads_advertiser");

        // Statystyki budżetowe
        $budgetStats = $this->db->fetchOne("
            SELECT
                SUM(budget) as total_budget,
                SUM(spent) as total_spent,
                AVG(budget) as avg_budget,
                COUNT(*) as campaigns_with_budget
            FROM ads
            WHERE budget > 0
        ");

        $stats['total_budget'] = $budgetStats['total_budget'] ?? 0;
        $stats['total_spent'] = $budgetStats['total_spent'] ?? 0;
        $stats['avg_budget'] = $budgetStats['avg_budget'] ?? 0;
        $stats['campaigns_with_budget'] = $budgetStats['campaigns_with_budget'] ?? 0;

        // Reklamy dodane dzisiaj
        $stats['added_today'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM ads
            WHERE DATE(created_at) = DATE('now')
        ");

        // Reklamy dodane w tym tygodniu
        $stats['added_this_week'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM ads
            WHERE created_at >= DATE('now', '-7 days')
        ");

        return $stats;
    }

    /**
     * Renderuj widok
     */
    private function render($view, $data = []) {
        extract($data);

        ob_start();
        include ADMIN_PATH . '/views/' . $view . '.php';
        $content = ob_get_clean();

        include ADMIN_PATH . '/views/layout.php';
    }
}
