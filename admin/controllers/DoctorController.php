<?php

/**
 * DoctorController - zar<PERSON><PERSON><PERSON><PERSON><PERSON> lekarzami
 */
class DoctorController {
    private $db;
    private $doctorModel;

    public function __construct() {
        $this->db = Database::getInstance();
        $this->doctorModel = new Doctor();
    }

    /**
     * Lista lekarzy
     */
    public function index() {
        $doctors = $this->doctorModel->getAllForClient();

        // Upewnij się że ścieżki do zdjęć mają poprawny format
        foreach ($doctors as &$doctor) {
            if (!empty($doctor['photo_url'])) {
                $photoUrl = $doctor['photo_url'];
                // Upewnij się że ścieżka do zdjęcia zaczyna się od /uploads/
                if (!str_starts_with($photoUrl, '/uploads/')) {
                    // Jeśli ścieżka zaczyna się od uploads/ (bez leading slash), dodaj slash na początku
                    if (str_starts_with($photoUrl, 'uploads/')) {
                        $doctor['photo_url'] = '/' . $photoUrl;
                    }
                    // Jeś<PERSON> ścieżka nie zawiera uploads/ w ogóle, dodaj pełną ścieżkę
                    else if (!str_contains($photoUrl, 'uploads/')) {
                        $doctor['photo_url'] = '/uploads/' . $photoUrl;
                    }
                }
            }
        }

        $this->render('doctors/index', [
            'doctors' => $doctors,
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Formularz dodawania lekarza
     */
    public function create() {
        $this->render('doctors/create', [
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Zapisz nowego lekarza
     */
    public function store() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /admin/doctors');
            exit;
        }

        $firstName = trim($_POST['first_name'] ?? '');
        $lastName = trim($_POST['last_name'] ?? '');
        $specialization = trim($_POST['specialization'] ?? '');
        $photoUrl = null; // Default to null, will be set if a file is uploaded

        if (empty($firstName) || empty($lastName)) {
            $_SESSION['error'] = 'Imię i nazwisko są wymagane';
            header('Location: /admin/doctors/create');
            exit;
        }

        // Handle file upload
        if (!empty($_FILES['photo_upload']['name'])) {
            $file = $_FILES['photo_upload'];

            // Check if file is an image
            $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
            if (!in_array($file['type'], $allowedTypes)) {
                $_SESSION['error'] = 'Dozwolone są tylko pliki obrazów (JPEG, PNG, GIF, WebP)';
                header('Location: /admin/doctors/create');
                exit;
            }

            // Check file size (max 5MB)
            if ($file['size'] > 5 * 1024 * 1024) {
                $_SESSION['error'] = 'Rozmiar pliku nie może przekraczać 5MB';
                header('Location: /admin/doctors/create');
                exit;
            }

            // Create uploads directory if it doesn't exist
            $uploadDir = __DIR__ . '/../uploads/doctors';
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // Generate unique filename
            $fileExtension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $fileName = 'doctor_' . time() . '.' . $fileExtension;
            $filePath = $uploadDir . '/' . $fileName;

            // Move uploaded file
            if (move_uploaded_file($file['tmp_name'], $filePath)) {
                // Set photo URL to the uploaded file path
                $photoUrl = '/uploads/doctors/' . $fileName;
            } else {
                $_SESSION['error'] = 'Błąd podczas przesyłania pliku';
                header('Location: /admin/doctors/create');
                exit;
            }
        }

        try {
            $this->doctorModel->create($firstName, $lastName, $specialization, $photoUrl);
            $_SESSION['success'] = 'Lekarz został dodany pomyślnie';
            header('Location: /admin/doctors');
        } catch (Exception $e) {
            $_SESSION['error'] = 'Błąd podczas dodawania lekarza: ' . $e->getMessage();
            header('Location: /admin/doctors/create');
        }
        exit;
    }

    /**
     * Formularz edycji lekarza
     */
    public function edit() {
        $id = $_GET['id'] ?? null;
        if (!$id) {
            header('Location: /admin/doctors');
            exit;
        }

        $doctor = $this->doctorModel->getById($id);
        if (!$doctor) {
            $_SESSION['error'] = 'Lekarz nie został znaleziony';
            header('Location: /admin/doctors');
            exit;
        }

        // Upewnij się że ścieżka do zdjęcia ma poprawny format
        if (!empty($doctor['photo_url'])) {
            $photoUrl = $doctor['photo_url'];
            // Upewnij się że ścieżka do zdjęcia zaczyna się od /uploads/
            if (!str_starts_with($photoUrl, '/uploads/')) {
                // Jeśli ścieżka zaczyna się od uploads/ (bez leading slash), dodaj slash na początku
                if (str_starts_with($photoUrl, 'uploads/')) {
                    $doctor['photo_url'] = '/' . $photoUrl;
                }
                // Jeśli ścieżka nie zawiera uploads/ w ogóle, dodaj pełną ścieżkę
                else if (!str_contains($photoUrl, 'uploads/')) {
                    $doctor['photo_url'] = '/uploads/' . $photoUrl;
                }
            }
        }

        $this->render('doctors/edit', [
            'doctor' => $doctor,
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Aktualizuj lekarza
     */
    public function update() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /admin/doctors');
            exit;
        }

        $id = $_GET['id'] ?? null;
        if (!$id) {
            header('Location: /admin/doctors');
            exit;
        }

        $firstName = trim($_POST['first_name'] ?? '');
        $lastName = trim($_POST['last_name'] ?? '');
        $specialization = trim($_POST['specialization'] ?? '');
        $photoUrl = null; // Default to null, will be set if a file is uploaded

        if (empty($firstName) || empty($lastName)) {
            $_SESSION['error'] = 'Imię i nazwisko są wymagane';
            header('Location: /admin/doctors/edit/' . $id);
            exit;
        }

        // Handle file upload
        if (!empty($_FILES['photo_upload']['name'])) {
            $file = $_FILES['photo_upload'];

            // Check if file is an image
            $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
            if (!in_array($file['type'], $allowedTypes)) {
                $_SESSION['error'] = 'Dozwolone są tylko pliki obrazów (JPEG, PNG, GIF, WebP)';
                header('Location: /admin/doctors/edit/' . $id);
                exit;
            }

            // Check file size (max 5MB)
            if ($file['size'] > 5 * 1024 * 1024) {
                $_SESSION['error'] = 'Rozmiar pliku nie może przekraczać 5MB';
                header('Location: /admin/doctors/edit/' . $id);
                exit;
            }

            // Create uploads directory if it doesn't exist
            $uploadDir = __DIR__ . '/../uploads/doctors';
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // Generate unique filename
            $fileExtension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $fileName = 'doctor_' . $id . '_' . time() . '.' . $fileExtension;
            $filePath = $uploadDir . '/' . $fileName;

            // Move uploaded file
            if (move_uploaded_file($file['tmp_name'], $filePath)) {
                // Set photo URL to the uploaded file path
                $photoUrl = '/uploads/doctors/' . $fileName;
            } else {
                $_SESSION['error'] = 'Błąd podczas przesyłania pliku';
                header('Location: /admin/doctors/edit/' . $id);
                exit;
            }
        }

        try {
            $this->doctorModel->update($id, $firstName, $lastName, $specialization, $photoUrl);
            $_SESSION['success'] = 'Lekarz został zaktualizowany pomyślnie';
            header('Location: /admin/doctors');
        } catch (Exception $e) {
            $_SESSION['error'] = 'Błąd podczas aktualizacji lekarza: ' . $e->getMessage();
            header('Location: /admin/doctors/edit/' . $id);
        }
        exit;
    }

    /**
     * Usuń lekarza
     */
    public function delete() {
        $id = $_GET['id'] ?? null;
        if (!$id) {
            header('Location: /admin/doctors');
            exit;
        }

        try {
            $this->doctorModel->delete($id);
            $_SESSION['success'] = 'Lekarz został usunięty pomyślnie';
        } catch (Exception $e) {
            $_SESSION['error'] = 'Błąd podczas usuwania lekarza: ' . $e->getMessage();
        }

        header('Location: /admin/doctors');
        exit;
    }

    /**
     * Regeneruj kod dostępu lekarza
     */
    public function regenerateCode() {
        header('Content-Type: application/json');

        $id = $_GET['id'] ?? null;
        if (!$id) {
            echo json_encode(['success' => false, 'error' => 'Brak ID lekarza']);
            exit;
        }

        try {
            $newAccessCode = $this->doctorModel->regenerateAccessCode($id);
            if ($newAccessCode) {
                echo json_encode(['success' => true, 'access_code' => $newAccessCode]);
            } else {
                echo json_encode(['success' => false, 'error' => 'Nie udało się wygenerować nowego kodu']);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }

    /**
     * API - statystyki lekarza
     */
    public function getStats() {
        $doctorId = $_GET['doctor_id'] ?? null;
        $date = $_GET['date'] ?? date('Y-m-d');

        if (!$doctorId) {
            http_response_code(400);
            echo json_encode(['error' => 'Brak ID lekarza']);
            exit;
        }

        try {
            $stats = $this->doctorModel->getStats($doctorId, $date);
            echo json_encode(['success' => true, 'stats' => $stats]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
        exit;
    }

    /**
     * Renderuj widok
     */
    private function render($view, $data = []) {
        extract($data);

        ob_start();
        include ADMIN_PATH . '/views/' . $view . '.php';
        $content = ob_get_clean();

        include ADMIN_PATH . '/views/layout.php';
    }
}
