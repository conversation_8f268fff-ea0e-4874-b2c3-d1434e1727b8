<!-- Ustawienia systemu -->
<div class="px-6 py-4">
    <!-- Header -->
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-slate-900">Ustawienia systemu</h1>
        <p class="text-slate-600 mt-1">Konfiguracja systemu kolejkowego KtoOstatni</p>
    </div>

    <!-- Komunikaty -->
    <?php if (isset($success)): ?>
        <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex items-center">
                <span class="material-icons-outlined text-green-600 mr-2">check_circle</span>
                <span class="text-green-800"><?= htmlspecialchars($success) ?></span>
            </div>
        </div>
    <?php endif; ?>

    <?php if (isset($error)): ?>
        <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div class="flex items-center">
                <span class="material-icons-outlined text-red-600 mr-2">error</span>
                <span class="text-red-800"><?= htmlspecialchars($error) ?></span>
            </div>
        </div>
    <?php endif; ?>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Ustawienia ogólne -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Ustawienia ogólne -->
            <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
                <h2 class="text-lg font-semibold text-slate-900 mb-4">Ustawienia ogólne</h2>

                <form method="POST" class="space-y-4">
                    <input type="hidden" name="action" value="update_general">

                    <div>
                        <!-- UWAGA: Kolumna company_name została usunięta z users -->
                        <!-- <label for="company_name" class="block text-sm font-medium text-slate-700 mb-2">
                            Nazwa firmy
                        </label>
                        <input type="text"
                               id="company_name"
                               name="company_name"
                               value="<?= htmlspecialchars($settings['company_name']) ?>"
                               class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                    
                    <div>
                        <label for="default_appointment_duration" class="block text-sm font-medium text-slate-700 mb-2">
                            Domyślny czas wizyty (minuty)
                        </label>
                        <input type="number" 
                               id="default_appointment_duration" 
                               name="default_appointment_duration" 
                               value="<?= $settings['default_appointment_duration'] ?>"
                               min="5" max="120"
                               class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                    
                    <div>
                        <label for="auto_refresh_interval" class="block text-sm font-medium text-slate-700 mb-2">
                            Interwał odświeżania (sekundy) - obecnie wyłączony
                        </label>
                        <input type="number" 
                               id="auto_refresh_interval" 
                               name="auto_refresh_interval" 
                               value="<?= $settings['auto_refresh_interval'] ?>"
                               min="10" max="300" disabled
                               class="w-full px-3 py-2 border border-slate-300 rounded-lg bg-slate-100 text-slate-500">
                        <p class="text-xs text-slate-500 mt-1">Automatyczne odświeżanie zostało wyłączone na żądanie użytkownika</p>
                    </div>
                    
                    <button type="submit" 
                            class="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                        Zapisz ustawienia ogólne
                    </button>
                </form>
            </div>

            <!-- Ustawienia kolejki -->
                        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
                            <h2 class="text-lg font-semibold text-slate-900 mb-4">Ustawienia kolejki</h2>

                            <form method="POST" class="space-y-4">
                                <input type="hidden" name="action" value="update_queue">

                                <div>
                                    <label for="max_waiting_time" class="block text-sm font-medium text-slate-700 mb-2">
                                        Maksymalny czas oczekiwania (minuty)
                                    </label>
                                    <input type="number"
                                        id="max_waiting_time"
                                        name="max_waiting_time"
                                        value="<?= $settings['max_waiting_time'] ?>"
                                        min="15" max="480"
                                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                </div>

                                <div class="space-y-3">
                                    <div class="flex items-center">
                                        <input type="checkbox"
                                            id="enable_sms_notifications"
                                            name="enable_sms_notifications"
                                            <?= $settings['enable_sms_notifications'] ? 'checked' : '' ?>
                                            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-slate-300 rounded">
                                        <label for="enable_sms_notifications" class="ml-2 text-sm text-slate-700">
                                            Włącz powiadomienia SMS
                                        </label>
                                    </div>

                                    <div class="flex items-center">
                                        <input type="checkbox"
                                            id="enable_email_notifications"
                                            name="enable_email_notifications"
                                            <?= $settings['enable_email_notifications'] ? 'checked' : '' ?>
                                            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-slate-300 rounded">
                                        <label for="enable_email_notifications" class="ml-2 text-sm text-slate-700">
                                            Włącz powiadomienia email
                                        </label>
                                    </div>
                                </div>

                                <button type="submit"
                                    class="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                                    Zapisz ustawienia kolejki
                                </button>
                            </form>
                        </div>

                        <!-- Ustawienia statusów -->
                        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
                            <h2 class="text-lg font-semibold text-slate-900 mb-4">Ustawienia statusów</h2>

                            <form method="POST" class="space-y-4">
                                <input type="hidden" name="action" value="update_status_settings">

                                <div class="space-y-3">
                                    <div class="flex items-center">
                                        <input type="checkbox"
                                            id="enable_appointment_confirmation"
                                            name="enable_appointment_confirmation"
                                            <?= $settings['enable_appointment_confirmation'] ? 'checked' : '' ?>
                                            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-slate-300 rounded">
                                        <label for="enable_appointment_confirmation" class="ml-2 text-sm text-slate-700">
                                            Włącz potwierdzanie wizyt
                                        </label>
                                    </div>

                                    <div class="flex items-center">
                                        <input type="checkbox"
                                            id="enable_attendance_confirmation"
                                            name="enable_attendance_confirmation"
                                            <?= $settings['enable_attendance_confirmation'] ? 'checked' : '' ?>
                                            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-slate-300 rounded">
                                        <label for="enable_attendance_confirmation" class="ml-2 text-sm text-slate-700">
                                            Włącz potwierdzanie obecnosci
                                        </label>
                                    </div>

                                    <div class="flex items-center">
                                        <input type="checkbox"
                                            id="enable_sms_sending"
                                            name="enable_sms_sending"
                                            <?= $settings['enable_sms_sending'] ? 'checked' : '' ?>
                                            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-slate-300 rounded">
                                        <label for="enable_sms_sending" class="ml-2 text-sm text-slate-700">
                                            Włącz wysyłanie SMS
                                        </label>
                                    </div>
                                </div>

                                <button type="submit"
                                    class="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                                    Zapisz ustawienia statusów
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Panel informacyjny -->
                    <div class="space-y-6">
                        <!-- Informacje o systemie -->
                        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
                            <h2 class="text-lg font-semibold text-slate-900 mb-4">Informacje o systemie</h2>

                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-sm text-slate-600">Wersja systemu:</span>
                                    <span class="text-sm font-medium text-slate-900"><?= $settings['system_version'] ?></span>
                                </div>

                                <div class="flex justify-between">
                                    <span class="text-sm text-slate-600">Rozmiar bazy:</span>
                                    <span class="text-sm font-medium text-slate-900"><?= $settings['database_size'] ?></span>
                                </div>

                                <div class="flex justify-between">
                                    <span class="text-sm text-slate-600">Liczba wizyt:</span>
                                    <span class="text-sm font-medium text-slate-900"><?= number_format($settings['total_appointments']) ?></span>
                                </div>

                                <div class="flex justify-between">
                                    <span class="text-sm text-slate-600">Aktywni lekarze:</span>
                                    <span class="text-sm font-medium text-slate-900"><?= $settings['total_doctors'] ?></span>
                                </div>
                            </div>
                        </div>

                        <!-- Akcje systemowe -->
                        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
                            <h2 class="text-lg font-semibold text-slate-900 mb-4">Akcje systemowe</h2>

                            <div class="space-y-3">
                                <form method="POST" class="w-full">
                                    <input type="hidden" name="action" value="clear_cache">
                                    <button type="submit"
                                        class="w-full px-4 py-2 bg-slate-600 text-white rounded-lg hover:bg-slate-700 transition-colors text-sm">
                                        <span class="material-icons-outlined text-sm mr-1">refresh</span>
                                        Wyczyść cache
                                    </button>
                                </form>

                                <a href="/admin/pulpit"
                                    class="block w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors text-sm text-center">
                                    <span class="material-icons-outlined text-sm mr-1">dashboard</span>
                                    Powrót do pulpitu
                                </a>
                            </div>
                        </div>
                    </div>
            </div>
        </div>