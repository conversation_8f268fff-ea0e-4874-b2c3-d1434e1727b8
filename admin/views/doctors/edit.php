<!-- <PERSON><PERSON><PERSON><PERSON> lekarza -->
<div class="px-6 py-4">
    <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-slate-900">Edytu<PERSON> lekarza</h1>
            <p class="text-slate-600 mt-1"><PERSON><PERSON><PERSON> dane lekarza</p>
        </div>

        <!-- Komunikaty -->
        <?php if (isset($error)): ?>
            <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div class="flex items-center">
                    <span class="material-icons-outlined text-red-600 mr-2">error</span>
                    <span class="text-red-800"><?= htmlspecialchars($error) ?></span>
                </div>
            </div>
        <?php endif; ?>

        <?php if (isset($success)): ?>
            <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                <div class="flex items-center">
                    <span class="material-icons-outlined text-green-600 mr-2">check_circle</span>
                    <span class="text-green-800"><?= htmlspecialchars($success) ?></span>
                </div>
            </div>
        <?php endif; ?>

        <!-- Formularz -->
        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <form method="POST" enctype="multipart/form-data" class="space-y-6">
                <!-- Zdjęcie -->
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-2">
                        Zdjęcie lekarza
                    </label>
                    <div class="flex items-center space-x-4">
                        <img id="photo-preview" src="<?= getDoctorPhotoUrl($doctor['photo_url']) ?>"
                            alt="<?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="flex-1">
                            <div class="mb-3">
                                <label for="photo_upload" class="block text-sm font-medium text-slate-700 mb-2">
                                    Prześlij zdjęcie
                                </label>
                                <div class="relative">
                                    <input type="file"
                                        id="photo_upload"
                                        name="photo_upload"
                                        accept="image/*"
                                        onchange="previewPhoto(this)"
                                        class="absolute inset-0 w-full h-full opacity-0 cursor-pointer">
                                    <div class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors inline-flex items-center" title="Wybierz plik zdjęcia z komputera">
                                        <span class="material-icons-outlined text-sm mr-2">cloud_upload</span>
                                        Wybierz plik
                                    </div>
                                </div>
                                <p class="text-xs text-slate-500 mt-2">Dozwolone formaty: JPG, PNG, GIF, WebP. Maksymalny rozmiar: 5MB</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Imię -->
                <div>
                    <label for="first_name" class="block text-sm font-medium text-slate-700 mb-2">
                        Imię *
                    </label>
                    <input type="text"
                        id="first_name"
                        name="first_name"
                        value="<?= htmlspecialchars($doctor['first_name']) ?>"
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        required>
                </div>

                <!-- Nazwisko -->
                <div>
                    <label for="last_name" class="block text-sm font-medium text-slate-700 mb-2">
                        Nazwisko *
                    </label>
                    <input type="text"
                        id="last_name"
                        name="last_name"
                        value="<?= htmlspecialchars($doctor['last_name']) ?>"
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        required>
                </div>

                <!-- Specjalizacja -->
                <div>
                    <label for="specialization" class="block text-sm font-medium text-slate-700 mb-2">
                        Specjalizacja
                    </label>
                    <input type="text"
                        id="specialization"
                        name="specialization"
                        value="<?= htmlspecialchars($doctor['specialization']) ?>"
                        placeholder="np. Kardiolog, Dermatolog"
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                </div>

                <!-- Informacje dodatkowe -->
                <div class="bg-slate-50 rounded-lg p-4">
                    <h3 class="font-medium text-slate-900 mb-2">Informacje</h3>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-slate-600">ID lekarza:</span>
                            <span class="ml-2 font-medium"><?= $doctor['id'] ?></span>
                        </div>
                        <div>
                            <span class="text-slate-600">Status:</span>
                            <span class="ml-2">
                                <?php if ($doctor['active']): ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Aktywny
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Nieaktywny
                                    </span>
                                <?php endif; ?>
                            </span>
                        </div>
                        <div>
                            <span class="text-slate-600">Utworzony:</span>
                            <span class="ml-2"><?= formatDateTime($doctor['created_at']) ?></span>
                        </div>
                        <?php if (isset($doctor['updated_at']) && $doctor['updated_at']): ?>
                            <div>
                                <span class="text-slate-600">Zaktualizowany:</span>
                                <span class="ml-2"><?= formatDateTime($doctor['updated_at']) ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Przyciski -->
                <div class="flex items-center justify-between pt-6 border-t border-slate-200">
                    <a href="/admin/lekarze"
                        class="px-4 py-2 text-slate-600 hover:text-slate-800 transition-colors" title="Powrót do listy lekarzy bez zapisywania zmian">
                        ← Powrót do listy lekarzy
                    </a>

                    <div class="flex gap-3">
                        <button type="button"
                            onclick="deleteDoctor(<?= $doctor['id'] ?>, '<?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>')"
                            class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors" title="Usuń lekarza z systemu (operacja nieodwracalna)">
                            <span class="material-icons-outlined text-sm mr-1">delete</span>
                            Usuń lekarza
                        </button>

                        <button type="submit"
                            class="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors" title="Zapisz wprowadzone zmiany">
                            <span class="material-icons-outlined text-sm mr-1">save</span>
                            Zapisz zmiany
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    function deleteDoctor(doctorId, doctorName) {
        if (confirm('Czy na pewno chcesz usunąć lekarza "' + doctorName + '"? Ta operacja jest nieodwracalna.')) {
            window.location.href = '/admin/lekarze/delete/' + doctorId;
        }
    }

    function previewPhoto(input) {
        if (input.files && input.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                document.getElementById('photo-preview').src = e.target.result;
            }

            reader.readAsDataURL(input.files[0]);
        }
    }
</script>