<!-- Date Navigation and Stats -->
<div class="px-6 py-4 overflow-x-hidden">
    <!-- Single Controls Row -->
    <div class="flex flex-wrap items-center gap-3 mb-4">
        <!-- Date Navigation -->
        <button onclick="changeDate(-1)" class="px-3 py-2 bg-white border border-slate-300 rounded-lg hover:bg-slate-50 transition-colors" title="Przejdź do poprzedniego dnia">
            <span class="material-icons-outlined text-slate-600 text-sm">chevron_left</span>
        </button>
        <input type="date" id="date-picker" value="<?= $selectedDate ?>" onchange="selectDate()"
            class="px-3 py-2 bg-white border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
            title="Wybierz datę do wyświetlenia">
        <button onclick="changeDate(1)" class="px-3 py-2 bg-white border border-slate-300 rounded-lg hover:bg-slate-50 transition-colors" title="Przejd<PERSON> do następnego dnia">
            <span class="material-icons-outlined text-slate-600 text-sm">chevron_right</span>
        </button>
        <!-- Action Buttons -->
        <a href="/admin/appointments/create"
            class="flex items-center gap-2 px-3 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors text-sm"
            title="Dodaj nową wizytę do systemu">
            <span class="material-icons-outlined text-sm">add</span>
            Dodaj wizytę
        </a>
        <a href="/admin/pulpit"
            class="flex items-center gap-2 px-3 py-2 bg-slate-600 text-white rounded-lg hover:bg-slate-700 transition-colors text-sm"
            title="Powrót do pulpitu głównego">
            <span class="material-icons-outlined text-sm">dashboard</span>
            Pulpit
        </a>

        <!-- Filters -->
        <div class="flex items-center gap-2 px-3 py-2 bg-white border border-slate-200 rounded-lg">
            <span class="text-sm text-slate-700">Pokaż:</span>
            <label class="flex items-center gap-1 cursor-pointer" title="Pokaż lekarzy z wizytami">
                <input type="checkbox" id="filter-with-appointments" checked onchange="toggleFilter()"
                    class="rounded border-slate-300 text-indigo-600 focus:ring-indigo-500">
                <span class="text-xs text-slate-600">Z wizytami</span>
            </label>
            <label class="flex items-center gap-1 cursor-pointer" title="Pokaż lekarzy bez wizyt">
                <input type="checkbox" id="filter-without-appointments" checked onchange="toggleFilter()"
                    class="rounded border-slate-300 text-indigo-600 focus:ring-indigo-500">
                <span class="text-xs text-slate-600">Bez wizyt</span>
            </label>
            <label class="flex items-center gap-1 cursor-pointer" title="Pokaż tylko pracujących lekarzy">
                <input type="checkbox" id="filter-working" checked onchange="toggleFilter()"
                    class="rounded border-slate-300 text-indigo-600 focus:ring-indigo-500">
                <span class="text-xs text-slate-600">Pracujący</span>
            </label>
        </div>

        <!-- Stats -->
        <div class="flex items-center gap-3 px-3 py-2 bg-white border border-slate-200 rounded-lg ml-auto">
            <div class="text-center">
                <div class="text-sm font-bold text-slate-900"><?= count($doctors) ?></div>
                <div class="text-xs text-slate-500">Lekarzy</div>
            </div>
            <div class="text-center">
                <div class="text-sm font-bold text-green-600"><?= $stats['working_doctors'] ?></div>
                <div class="text-xs text-slate-500">Pracuje</div>
            </div>
            <div class="text-center">
                <div class="text-sm font-bold text-blue-600"><?= $stats['total_patients'] ?></div>
                <div class="text-xs text-slate-500">Pacjentów</div>
            </div>
            <div class="text-center">
                <div class="text-sm font-bold <?= $averageDelay > 0 ? 'text-red-600' : 'text-green-600' ?>">
                    <?= $averageDelay > 0 ? '+' . round($averageDelay) . ' min' : 'Na czasie' ?>
                </div>
                <div class="text-xs text-slate-500">Opóźnienie</div>
            </div>
        </div>
    </div>

    <?php if (empty($doctors)): ?>
        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-8 text-center">
            <span class="material-icons-outlined text-slate-400 text-4xl mb-4">event_busy</span>
            <h3 class="text-lg font-semibold text-slate-900 mb-2">Brak wizyt na wybrany dzień</h3>
            <p class="text-slate-600 mb-4">Nie znaleziono żadnych wizyt dla lekarzy na dzień <?= formatDate($selectedDate) ?></p>
            <a href="/admin/appointments/create" class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors" title="Dodaj pierwszą wizytę do systemu">
                <span class="material-icons-outlined text-sm mr-2">add</span>
                Dodaj pierwszą wizytę
            </a>
        </div>
    <?php else: ?>
        <?php
        // Podziel lekarzy na tych z wizytami i bez wizyt
        $doctorsWithAppointments = array_filter($doctors, function ($doctor) {
            return $doctor['appointments_count'] > 0;
        });
        $doctorsWithoutAppointments = array_filter($doctors, function ($doctor) {
            return $doctor['appointments_count'] == 0;
        });
        ?>

        <?php if (!empty($doctorsWithAppointments)): ?>
            <!-- Lekarze z wizytami -->
            <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-4 mb-6">
                <?php foreach ($doctorsWithAppointments as $doctor): ?>
                    <!-- Doctor Card -->
                    <div class="bg-white rounded-lg shadow-sm border border-slate-200 doctor-card"
                        data-has-appointments="<?= $doctor['appointments_count'] > 0 ? 'true' : 'false' ?>"
                        data-status="<?= $doctor['status'] ?>">
                        <!-- Doctor Header - Fixed Height -->
                        <div class="p-4 border-b border-slate-200 <?= getDoctorHeaderClass($doctor['status']) ?>" style="height: 6rem;">
                            <div class="flex items-start justify-between h-full">
                                <div class="flex items-start gap-3 flex-1 min-w-0">
                                    <img class="h-12 w-12 rounded-full object-cover flex-shrink-0"
                                        src="<?= $doctor['photo_url'] ?>"
                                        alt="<?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>"
                                        onerror="this.src='<?= DOCTORS_PHOTOS_URL ?>/default-avatar.png'">
                                    <div class="flex-1 min-w-0">
                                        <!-- Doctor Name - Truncated -->
                                        <h3 class="font-semibold text-slate-900 text-sm leading-tight truncate"
                                            title="<?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>">
                                            <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                                        </h3>

                                        <!-- Specialization - Truncated to 1 line -->
                                        <p class="text-xs text-slate-600 leading-tight mt-1 truncate"
                                            title="<?= htmlspecialchars($doctor['specialization']) ?>">
                                            <?= htmlspecialchars($doctor['specialization']) ?>
                                        </p>

                                        <!-- Room Info -->
                                        <?php if ($doctor['default_room_name']): ?>
                                            <p class="text-xs text-slate-500 mt-1">
                                                <?= htmlspecialchars($doctor['default_room_name']) ?>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Stats Column -->
                                <div class="text-right flex-shrink-0 ml-3">
                                    <div class="text-lg font-bold text-slate-900"><?= $doctor['appointments_count'] ?></div>
                                    <div class="text-xs text-slate-600">pacjentów</div>
                                    <?php if ($doctor['delay_minutes'] > 0): ?>
                                        <div class="text-xs text-red-600 font-medium">+<?= round($doctor['delay_minutes']) ?> min</div>
                                    <?php else: ?>
                                        <div class="text-xs text-green-600 font-medium">Na czasie</div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Queue List -->
                        <div class="max-h-96 overflow-y-auto custom-scroll">
                            <div class="p-2 space-y-1">
                                <?php if (empty($doctor['waiting_appointments'])): ?>
                                    <div class="p-4 text-center text-slate-500">
                                        <span class="material-icons-outlined text-2xl mb-2">event_available</span>
                                        <p class="text-sm">Brak oczekujących pacjentów</p>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($doctor['waiting_appointments'] as $appointment): ?>
                                        <?php
                                        // Normalizuj dane pacjenta
                                        $normalizedPatientName = normalizePatientName($appointment['patient_name']);

                                        // Oblicz opóźnienie względem daty wizyty
                                        $delay = calculateDelay($appointment['appointment_time'], $selectedDate, $appointment['status']);
                                        $isDelayed = !empty($delay);

                                        // Określ status wizyty i klasę CSS
                                        switch ($appointment['status']) {
                                            case 'current':
                                                $cardClass = 'patient-current border-l-4 border-green-400 bg-green-50';
                                                break;
                                            case 'completed':
                                            case 'closed':
                                                $cardClass = 'patient-completed border-l-4 border-gray-400 bg-gray-100';
                                                break;
                                            case 'waiting':
                                            default:
                                                $cardClass = $isDelayed ? 'patient-delayed border-l-4 border-red-400' : 'patient-waiting border-l-4 border-blue-400';
                                                break;
                                        }
                                        ?>
                                        <div class="<?= $cardClass ?> p-1.5 rounded hover:bg-slate-50 transition-colors overflow-hidden">
                                            <!-- Compact Patient Row with Status Icons -->
                                            <div class="flex items-center gap-2 w-full">
                                                <!-- Time -->
                                                <div class="flex-shrink-0 w-10 cursor-pointer hover:bg-slate-50 rounded px-1 py-1"
                                                    onclick="showAppointmentDetails(<?= $appointment['id'] ?>)"
                                                    title="Kliknij aby zobaczyć szczegóły wizyty">
                                                    <span class="text-sm font-bold text-slate-900">
                                                        <?= formatTime($appointment['appointment_time']) ?>
                                                    </span>
                                                </div>

                                                <!-- Patient Name -->
                                                <div class="flex-1 min-w-0 cursor-pointer hover:bg-slate-50 rounded px-1 py-1 overflow-hidden"
                                                    onclick="showAppointmentDetails(<?= $appointment['id'] ?>)"
                                                    title="Kliknij aby zobaczyć szczegóły wizyty">
                                                    <div class="font-medium text-sm text-slate-900 truncate">
                                                        <?= htmlspecialchars($normalizedPatientName) ?>
                                                    </div>
                                                    <?php if (!empty($appointment['notes'])): ?>
                                                        <div class="text-xs text-slate-600 truncate">
                                                            <?= htmlspecialchars($appointment['notes']) ?>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- Delay -->
                                                <div class="flex-shrink-0 w-12 text-right">
                                                    <?php if ($isDelayed): ?>
                                                        <span class="text-xs text-red-600 font-medium">
                                                            <?= $delay ?>
                                                        </span>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- Status Icons -->
                                                <div class="flex items-center gap-0.5 flex-shrink-0 w-12">
                                                    <!-- Confirmed - pokazuj tylko jeśli włączone w ustawieniach -->
                                                    <?php if ($settings['enable_appointment_confirmation']): ?>
                                                        <?php if ($appointment['is_confirmed']): ?>
                                                            <span class="material-icons-outlined text-green-600 text-sm cursor-pointer hover:text-green-700" title="Wizyta potwierdzona (kliknij aby zmienić)" onclick="toggleAppointmentStatus(<?= $appointment['id'] ?>, 'confirmed', false, event)">check_circle</span>
                                                        <?php else: ?>
                                                            <span class="material-icons-outlined text-gray-400 text-sm cursor-pointer hover:text-gray-500" title="Wizyta niepotwierdzona (kliknij aby zmienić)" onclick="toggleAppointmentStatus(<?= $appointment['id'] ?>, 'confirmed', true, event)">radio_button_unchecked</span>
                                                        <?php endif; ?>
                                                    <?php endif; ?>

                                                    <!-- Patient Present - pokazuj tylko jeśli włączone w ustawieniach -->
                                                    <?php if ($settings['enable_attendance_confirmation']): ?>
                                                        <?php if ($appointment['is_patient_present']): ?>
                                                            <span class="material-icons-outlined text-blue-600 text-sm cursor-pointer hover:text-blue-700" title="Pacjent obecny (kliknij aby zmienić)" onclick="toggleAppointmentStatus(<?= $appointment['id'] ?>, 'present', false, event)">person</span>
                                                        <?php else: ?>
                                                            <span class="material-icons-outlined text-gray-400 text-sm cursor-pointer hover:text-gray-500" title="Pacjent nieobecny (kliknij aby zmienić)" onclick="toggleAppointmentStatus(<?= $appointment['id'] ?>, 'present', true, event)">person_outline</span>
                                                        <?php endif; ?>
                                                    <?php endif; ?>

                                                    <!-- SMS Sent - pokazuj tylko jeśli włączone w ustawieniach -->
                                                    <?php if ($settings['enable_sms_sending']): ?>
                                                        <?php if ($appointment['sms_sent']): ?>
                                                            <span class="material-icons-outlined text-purple-600 text-sm cursor-pointer hover:text-purple-700" title="SMS wysłany (kliknij aby zmienić)" onclick="toggleAppointmentStatus(<?= $appointment['id'] ?>, 'sms', false, event)">sms</span>
                                                        <?php else: ?>
                                                            <span class="material-icons-outlined text-gray-400 text-sm cursor-pointer hover:text-gray-500" title="SMS niewysłany (kliknij aby zmienić)" onclick="toggleAppointmentStatus(<?= $appointment['id'] ?>, 'sms', true, event)">sms_failed</span>
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                </div>
                                                <!-- Action Button -->
                                                <div class="flex-shrink-0">
                                                    <?php if ($appointment['status'] == 'current'): ?>
                                                        <button onclick="event.stopPropagation(); completeAppointment(<?= $appointment['id'] ?>)"
                                                            class="p-1.5 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
                                                            title="Zakończ wizytę i oznacz jako zakończoną">
                                                            <span class="material-icons-outlined text-sm">check</span>
                                                        </button>
                                                    <?php else: ?>
                                                        <button onclick="event.stopPropagation(); callPatient(<?= $appointment['id'] ?>)"
                                                            class="p-1.5 <?= ($appointment['status'] == 'completed' || $appointment['status'] == 'closed') ? 'bg-indigo-800 text-white' : 'bg-indigo-600 text-white' ?> rounded hover:opacity-80 transition-colors"
                                                            title="Wezwij pacjenta do gabinetu">
                                                            <span class="material-icons-outlined text-sm">campaign</span>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($doctorsWithoutAppointments)): ?>
            <!-- Lekarze bez wizyt -->
            <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-4">
                <?php foreach ($doctorsWithoutAppointments as $doctor): ?>
                    <!-- Doctor Card -->
                    <div class="bg-white rounded-lg shadow-sm border border-slate-200 doctor-card"
                        data-has-appointments="false"
                        data-status="<?= $doctor['status'] ?>">
                        <!-- Doctor Header - Fixed Height -->
                        <div class="p-4 border-b border-slate-200 bg-gradient-to-r from-slate-50 to-gray-50" style="height: 6rem;">
                            <div class="flex items-start justify-between h-full">
                                <div class="flex items-start gap-3 flex-1 min-w-0">
                                    <img class="h-12 w-12 rounded-full object-cover flex-shrink-0"
                                        src="<?= htmlspecialchars($doctor['photo_url']) ?>"
                                        alt="<?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>"
                                        onerror="this.src='http://localhost:8010/uploads/doctors/default-avatar.png'">
                                    <div class="flex-1 min-w-0">
                                        <!-- Doctor Name - Truncated -->
                                        <h3 class="font-semibold text-slate-900 text-sm leading-tight truncate"
                                            title="<?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>">
                                            <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                                        </h3>

                                        <!-- Specialization - Truncated to 1 line -->
                                        <p class="text-xs text-slate-600 leading-tight mt-1 truncate"
                                            title="<?= htmlspecialchars($doctor['specialization']) ?>">
                                            <?= htmlspecialchars($doctor['specialization']) ?>
                                        </p>

                                        <!-- Room Info -->
                                        <?php if (!empty($doctor['room_name'])): ?>
                                            <p class="text-xs text-slate-500 mt-1">
                                                <?= htmlspecialchars($doctor['room_name']) ?>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Stats Column -->
                                <div class="text-right flex-shrink-0 ml-3">
                                    <div class="text-lg font-bold text-slate-900">0</div>
                                    <div class="text-xs text-slate-600">pacjentów</div>
                                    <div class="text-xs text-green-600 font-medium">Na czasie</div>
                                </div>
                            </div>
                        </div>

                        <!-- Queue List -->
                        <div class="max-h-96 overflow-y-auto custom-scroll">
                            <div class="p-2 space-y-1">
                                <div class="p-4 text-center text-slate-500">
                                    <span class="material-icons-outlined text-2xl mb-2">event_available</span>
                                    <p class="text-sm">Brak oczekujących pacjentów</p>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<?php
// Helper functions for the view
function getVisitDuration($startedAt) {
    $start = new DateTime($startedAt);
    $now = new DateTime();
    $diff = $now->diff($start);
    return $diff->i + ($diff->h * 60);
}
?>

<!-- Modal dla szczegółów wizyty -->
<div id="appointmentModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold text-slate-900">Szczegóły wizyty</h2>
                    <button onclick="closeAppointmentModal()" class="p-2 hover:bg-slate-100 rounded-lg" title="Zamknij okno szczegółów wizyty">
                        <span class="material-icons-outlined text-slate-600">close</span>
                    </button>
                </div>

                <div id="appointmentDetails" class="space-y-4">
                    <!-- Szczegóły będą załadowane przez AJAX -->
                </div>

                <div class="flex justify-end gap-3 mt-6 pt-4 border-t border-slate-200">
                    <button onclick="closeAppointmentModal()"
                        class="px-4 py-2 text-slate-600 hover:text-slate-800 transition-colors" title="Zamknij bez zapisywania zmian">
                        Zamknij
                    </button>
                    <a id="editAppointmentBtn" href="#"
                        class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors" title="Edytuj szczegóły wizyty">
                        Edytuj wizytę
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function showAppointmentDetails(appointmentId) {
        // Pokaż modal
        document.getElementById('appointmentModal').classList.remove('hidden');

        // Załaduj szczegóły
        fetch('/admin/appointments/details/' + appointmentId)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayAppointmentDetails(data.appointment);
                } else {
                    document.getElementById('appointmentDetails').innerHTML =
                        '<div class="text-red-600">Błąd: ' + data.message + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('appointmentDetails').innerHTML =
                    '<div class="text-red-600">Błąd podczas ładowania szczegółów</div>';
            });
    }

    function displayAppointmentDetails(appointment) {
        const detailsHtml = `
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <h3 class="font-semibold text-slate-900 mb-2">Informacje o pacjencie</h3>
                <div class="space-y-2">
                    <div><span class="text-slate-600">Imię i nazwisko:</span> <span class="font-medium">${appointment.normalized_patient_name}</span></div>
                    <div><span class="text-slate-600">Status wizyty:</span> <span class="font-medium">${appointment.status_label}</span></div>
                    ${appointment.delay ? '<div><span class="text-slate-600">Opóźnienie:</span> <span class="font-medium text-red-600">' + appointment.delay + '</span></div>' : ''}
                </div>
            </div>

            <div>
                <h3 class="font-semibold text-slate-900 mb-2">Informacje o lekarzu</h3>
                <div class="space-y-2">
                    <div><span class="text-slate-600">Lekarz:</span> <span class="font-medium">${appointment.doctor_name}</span></div>
                    ${appointment.doctor_specialization ? '<div><span class="text-slate-600">Specjalizacja:</span> <span class="font-medium">' + appointment.doctor_specialization + '</span></div>' : ''}
                </div>
            </div>

            <div>
                <h3 class="font-semibold text-slate-900 mb-2">Terminy</h3>
                <div class="space-y-2">
                    <div><span class="text-slate-600">Data:</span> <span class="font-medium">${appointment.formatted_date}</span></div>
                    <div><span class="text-slate-600">Godzina:</span> <span class="font-medium">${appointment.formatted_time}</span></div>
                </div>
            </div>

            <div>
                <h3 class="font-semibold text-slate-900 mb-2">Historia</h3>
                <div class="space-y-2 text-sm">
                    ${appointment.formatted_called_at ? '<div><span class="text-slate-600">Wywołana:</span> ' + appointment.formatted_called_at + '</div>' : ''}
                    ${appointment.formatted_completed_at ? '<div><span class="text-slate-600">Zakończona:</span> ' + appointment.formatted_completed_at + '</div>' : ''}
                </div>
            </div>
        </div>

        <div class="mt-4">
            <h3 class="font-semibold text-slate-900 mb-2">Statusy</h3>
            <div class="flex gap-4 text-sm">
                <div class="flex items-center gap-2">
                    ${appointment.is_confirmed ? '<span class="material-icons-outlined text-green-600 text-sm">check_circle</span>' : '<span class="material-icons-outlined text-gray-400 text-sm">radio_button_unchecked</span>'}
                    <span>Potwierdzona</span>
                </div>
                <div class="flex items-center gap-2">
                    ${(appointment.is_patient_present && appointment.status !== 'completed' && appointment.status !== 'closed') ? '<span class="material-icons-outlined text-blue-600 text-sm">person</span>' : '<span class="material-icons-outlined text-gray-400 text-sm">person_outline</span>'}
                    <span>Pacjent obecny</span>
                </div>
                <div class="flex items-center gap-2">
                    ${appointment.sms_sent ? '<span class="material-icons-outlined text-purple-600 text-sm">sms</span>' : '<span class="material-icons-outlined text-gray-400 text-sm">sms_failed</span>'}
                    <span>SMS wysłany</span>
                </div>
            </div>
        </div>
    `;

        document.getElementById('appointmentDetails').innerHTML = detailsHtml;
        document.getElementById('editAppointmentBtn').href = '/admin/appointments/edit/' + appointment.id;
    }

    function closeAppointmentModal() {
        document.getElementById('appointmentModal').classList.add('hidden');
    }

    // Zamknij modal po kliknięciu w tło
    document.getElementById('appointmentModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeAppointmentModal();
        }
    });

    // Zamknij modal klawiszem ESC
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const modal = document.getElementById('appointmentModal');
            if (modal && !modal.classList.contains('hidden')) {
                closeAppointmentModal();
            }
        }
    });

    // Date navigation
    function changeDate(direction) {
        const currentDate = new Date('<?= $selectedDate ?>');
        currentDate.setDate(currentDate.getDate() + direction);
        const newDate = currentDate.toISOString().split('T')[0];
        window.location.href = '?date=' + newDate;
    }

    function selectDate() {
        const dateInput = document.getElementById('date-picker');
        if (dateInput.value) {
            window.location.href = '?date=' + dateInput.value;
        }
    }

    // Filter functionality
    function toggleFilter() {
        const withAppointments = document.getElementById('filter-with-appointments').checked;
        const withoutAppointments = document.getElementById('filter-without-appointments').checked;
        const working = document.getElementById('filter-working').checked;

        document.querySelectorAll('.doctor-card').forEach(card => {
            const hasAppointments = card.dataset.hasAppointments === 'true';
            const status = card.dataset.status;

            let show = true;

            if (!withAppointments && hasAppointments) show = false;
            if (!withoutAppointments && !hasAppointments) show = false;
            if (!working && status !== 'active') show = false;

            card.style.display = show ? 'block' : 'none';
        });
    }

    // Call patient function
    function callPatient(appointmentId) {
        fetch('/admin/kolejki/call-patient', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    appointment_id: appointmentId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Błąd: ' + data.message);
                }
            });
    }

    // Complete appointment function
    function completeAppointment(appointmentId) {
        fetch('/admin/kolejki/complete-appointment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    appointment_id: appointmentId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Błąd: ' + data.message);
                }
            });
    }

    // Toggle patient presence function
    function togglePatientPresence(appointmentId, isPresent) {
        fetch('/admin/kolejki/toggle-patient-presence', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    appointment_id: appointmentId,
                    is_present: !isPresent
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Błąd: ' + data.message);
                }
            })
            .catch(error => {
                alert('Błąd podczas aktualizacji statusu obecności');
            });
    }

    // Toggle appointment status function
    function toggleAppointmentStatus(appointmentId, statusType, newValue, event) {
        // Store reference to the clicked element
        const iconElement = event.target;

        let fieldName = '';
        let fieldValue = newValue ? 1 : 0;

        // Determine which field to update based on status type
        switch (statusType) {
            case 'confirmed':
                fieldName = 'is_confirmed';
                break;
            case 'present':
                fieldName = 'is_patient_present';
                break;
            case 'sms':
                fieldName = 'sms_sent';
                break;
            default:
                console.error('Unknown status type:', statusType);
                return;
        }

        // Create form data
        const formData = new FormData();
        formData.append('appointment_id', appointmentId);
        formData.append('field_name', fieldName);
        formData.append('field_value', fieldValue);

        // Send AJAX request
        fetch('/admin/kolejki/toggle-appointment-status', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the icon without reloading the page
                    if (newValue) {
                        // Change to colored icon
                        switch (statusType) {
                            case 'confirmed':
                                iconElement.textContent = 'check_circle';
                                iconElement.className = 'material-icons-outlined text-green-600 text-sm cursor-pointer hover:text-green-700';
                                iconElement.setAttribute('title', 'Wizyta potwierdzona (kliknij aby zmienić)');
                                break;
                            case 'present':
                                iconElement.textContent = 'person';
                                iconElement.className = 'material-icons-outlined text-blue-600 text-sm cursor-pointer hover:text-blue-700';
                                iconElement.setAttribute('title', 'Pacjent obecny (kliknij aby zmienić)');
                                break;
                            case 'sms':
                                iconElement.textContent = 'sms';
                                iconElement.className = 'material-icons-outlined text-purple-600 text-sm cursor-pointer hover:text-purple-700';
                                iconElement.setAttribute('title', 'SMS wysłany (kliknij aby zmienić)');
                                break;
                        }
                    } else {
                        // Change to gray icon
                        switch (statusType) {
                            case 'confirmed':
                                iconElement.textContent = 'radio_button_unchecked';
                                iconElement.className = 'material-icons-outlined text-gray-400 text-sm cursor-pointer hover:text-gray-500';
                                iconElement.setAttribute('title', 'Wizyta niepotwierdzona (kliknij aby zmienić)');
                                break;
                            case 'present':
                                iconElement.textContent = 'person_outline';
                                iconElement.className = 'material-icons-outlined text-gray-400 text-sm cursor-pointer hover:text-gray-500';
                                iconElement.setAttribute('title', 'Pacjent nieobecny (kliknij aby zmienić)');
                                break;
                            case 'sms':
                                iconElement.textContent = 'sms_failed';
                                iconElement.className = 'material-icons-outlined text-gray-400 text-sm cursor-pointer hover:text-gray-500';
                                iconElement.setAttribute('title', 'SMS niewysłany (kliknij aby zmienić)');
                                break;
                        }
                    }

                    // Update onclick attribute with new value
                    iconElement.setAttribute('onclick', `toggleAppointmentStatus(${appointmentId}, '${statusType}', ${!newValue}, event)`);
                } else {
                    alert('Błąd: ' + (data.message || 'Nie udało się zmienić statusu'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Wystąpił błąd podczas zmiany statusu');
            });
    }
</script>