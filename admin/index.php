<?php

/**
 * Admin Panel Router
 * Obsługuje wszystkie żądania do panelu administracyjnego
 */

// Załaduj konfigurację
if (!defined('KTOOSTATNI_APP')) {
    define('KTOOSTATNI_APP', true);
}
require_once __DIR__ . '/../config.php';

// Debug
if (defined('DEBUG_MODE') && DEBUG_MODE) {
    error_log("Admin: Uruchamianie panelu administracyjnego");
    error_log("Admin: REQUEST_URI: " . $_SERVER['REQUEST_URI']);
}

// Pobierz ścieżkę z URL
$requestUri = $_SERVER['REQUEST_URI'];

// Usuń parametry GET z URL
$path = parse_url($requestUri, PHP_URL_PATH);

// Usuń początkowy i końcowy slash oraz /admin prefix
$path = trim($path, '/');
$path = preg_replace('#^admin/?#', '', $path);

// Jeś<PERSON>ż<PERSON> jest pusta, ustaw na pulpitu
if (empty($path)) {
    $path = 'pulpit';
}

// Routing
$routes = [
    // Autoryzacja
    'login' => ['AuthController', 'login'],
    'logout' => ['AuthController', 'logout'],

    // Panel główny
    'pulpit' => ['DashboardController', 'index'],
    'dashboard' => ['DashboardController', 'index'],

    // Zarządzanie kolejkami
    'queue' => ['QueueController', 'index'],
    'kolejki' => ['QueueController', 'index'],
    'queue/call-next' => ['QueueController', 'callNext'],
    'queue/call-patient' => ['QueueController', 'callPatient'],
    'queue/complete-appointment' => ['QueueController', 'completeAppointment'],
    'queue/toggle-patient-presence' => ['QueueController', 'togglePatientPresence'],
    'kolejki/call-next' => ['QueueController', 'callNext'],
    'kolejki/call-patient' => ['QueueController', 'callPatient'],
    'kolejki/complete-appointment' => ['QueueController', 'completeAppointment'],
    'kolejki/toggle-patient-presence' => ['QueueController', 'togglePatientPresence'],
    'kolejki/toggle-appointment-status' => ['QueueController', 'toggleAppointmentStatus'],

    // Zarządzanie lekarzami
    'doctors' => ['DoctorController', 'index'],
    'lekarze' => ['DoctorController', 'index'],
    'doctors/create' => ['DoctorController', 'create'],
    'lekarze/create' => ['DoctorController', 'create'],
    'doctors/store' => ['DoctorController', 'store'],
    'lekarze/store' => ['DoctorController', 'store'],

    // Zarządzanie wyświetlaczami
    'displays' => ['DisplayController', 'index'],
    'wyswietlacze' => ['DisplayController', 'index'],
    'displays/create' => ['DisplayController', 'create'],
    'wyswietlacze/create' => ['DisplayController', 'create'],
    'displays/store' => ['DisplayController', 'store'],
    'wyswietlacze/store' => ['DisplayController', 'store'],
    'displays/pair' => ['DisplayController', 'pair'],
    'wyswietlacze/pair' => ['DisplayController', 'pair'],
    'displays/update-name' => ['DisplayController', 'updateName'],
    'wyswietlacze/update-name' => ['DisplayController', 'updateName'],
    'displays/unpair' => ['DisplayController', 'unpair'],
    'wyswietlacze/unpair' => ['DisplayController', 'unpair'],
    'displays/delete' => ['DisplayController', 'delete'],
    'wyswietlacze/delete' => ['DisplayController', 'delete'],
    'doctors/edit' => ['DoctorController', 'edit'],
    'lekarze/edit' => ['DoctorController', 'edit'],
    'doctors/update' => ['DoctorController', 'update'],
    'lekarze/update' => ['DoctorController', 'update'],
    'doctors/delete' => ['DoctorController', 'delete'],
    'lekarze/delete' => ['DoctorController', 'delete'],
    'doctors/regenerate-code' => ['DoctorController', 'regenerateCode'],
    'lekarze/regenerate-code' => ['DoctorController', 'regenerateCode'],

    // Video management
    'video' => ['VideoController', 'index'],
    'video/all' => ['VideoController', 'allVideos'],
    'video/category' => ['VideoController', 'categoryVideos'],
    'video/edit-category' => ['VideoController', 'editCategory'],
    'video/update-category' => ['VideoController', 'updateCategory'],
    'video/approve-category' => ['VideoController', 'approveCategoryVideos'],
    'video/approve' => ['VideoController', 'approve'],
    'video/reject' => ['VideoController', 'reject'],
    'video/toggle-auto-accept' => ['VideoController', 'toggleAutoAccept'],
    'video/add-category' => ['VideoController', 'addCategory'],
    'video/delete-category' => ['VideoController', 'deleteCategory'],
    'video/get-video-data' => ['VideoController', 'getVideoData'],
    'video/get-advertisers' => ['VideoController', 'getAdvertisers'],
    'video/update' => ['VideoController', 'updateVideo'],
    'video/add' => ['VideoController', 'addVideo'],
    'video/store' => ['VideoController', 'storeVideo'],

    // Zarządzanie wizytami
    'appointments/create' => ['AppointmentController', 'create'],
    'appointments/store' => ['AppointmentController', 'store'],
    'appointments/edit' => ['AppointmentController', 'edit'],
    'appointments/update' => ['AppointmentController', 'update'],
    'appointments/delete' => ['AppointmentController', 'delete'],

    // Ustawienia
    'settings' => ['SettingsController', 'index'],
    'ustawienia' => ['SettingsController', 'index'],

    // API endpoints
    'api/queue-status' => ['QueueController', 'getQueueStatus'],
    'api/doctor-stats' => ['DoctorController', 'getStats'],
];

// Sprawdź czy route istnieje
if (!isset($routes[$path])) {
    // Sprawdź czy to route z parametrem (np. doctors/edit/1)
    $pathParts = explode('/', $path);
    $routeFound = false;

    if (count($pathParts) >= 3) {
        // Routes z ID na końcu
        $baseRoute = implode('/', array_slice($pathParts, 0, 2));
        $id = $pathParts[2];

        $paramRoutes = [
            'doctors/edit' => ['DoctorController', 'edit'],
            'lekarze/edit' => ['DoctorController', 'edit'],
            'doctors/update' => ['DoctorController', 'update'],
            'lekarze/update' => ['DoctorController', 'update'],
            'doctors/delete' => ['DoctorController', 'delete'],
            'lekarze/delete' => ['DoctorController', 'delete'],
            'doctors/regenerate-code' => ['DoctorController', 'regenerateCode'],
            'lekarze/regenerate-code' => ['DoctorController', 'regenerateCode'],

            // Video management
            'video' => ['VideoController', 'index'],
            'video/all' => ['VideoController', 'allVideos'],
            'video/category' => ['VideoController', 'categoryVideos'],
            'video/edit-category' => ['VideoController', 'editCategory'],
            'video/update-category' => ['VideoController', 'updateCategory'],
            'video/approve-category' => ['VideoController', 'approveCategoryVideos'],
            'video/approve' => ['VideoController', 'approve'],
            'video/reject' => ['VideoController', 'reject'],
            'video/toggle-auto-accept' => ['VideoController', 'toggleAutoAccept'],
            'video/add-category' => ['VideoController', 'addCategory'],
            'video/delete-category' => ['VideoController', 'deleteCategory'],
            'video/get-video-data' => ['VideoController', 'getVideoData'],
            'video/get-advertisers' => ['VideoController', 'getAdvertisers'],
            'video/update' => ['VideoController', 'updateVideo'],
            'video/add' => ['VideoController', 'addVideo'],
            'video/store' => ['VideoController', 'storeVideo'],
            'appointments/edit' => ['AppointmentController', 'edit'],
            'appointments/update' => ['AppointmentController', 'update'],
            'appointments/delete' => ['AppointmentController', 'delete'],
            'appointments/details' => ['AppointmentController', 'details'],
        ];

        if (isset($paramRoutes[$baseRoute])) {
            $routes[$path] = $paramRoutes[$baseRoute];
            $_GET['id'] = $id; // Dodaj ID do $_GET
            $routeFound = true;
        }
    }

    if (!$routeFound) {
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("Admin: Nie znaleziono routingu dla ścieżki: '$path'");
            error_log("Admin: Dostępne routes: " . implode(', ', array_keys($routes)));
        }
        http_response_code(404);
        die('Strona nie została znaleziona');
    }
}

// Pobierz controller i metodę
list($controllerName, $method) = $routes[$path];

// Sprawdź autoryzację (pomiń dla strony logowania)
if ($path !== 'login' && !isLoggedIn()) {
    redirect('/admin/login');
}

// Sprawdź czy controller istnieje
if (!class_exists($controllerName)) {
    http_response_code(500);
    die('Controller nie został znaleziony: ' . $controllerName);
}

// Utwórz instancję controllera
$controller = new $controllerName();

// Sprawdź czy metoda istnieje
if (!method_exists($controller, $method)) {
    http_response_code(500);
    die('Metoda nie została znaleziona: ' . $method);
}

// Wywołaj metodę
try {
    $controller->$method();
} catch (Exception $e) {
    if (DEBUG_MODE) {
        die('Błąd aplikacji: ' . $e->getMessage() . '<br>Plik: ' . $e->getFile() . '<br>Linia: ' . $e->getLine());
    } else {
        error_log('Błąd aplikacji: ' . $e->getMessage());
        http_response_code(500);
        die('Wystąpił błąd serwera');
    }
}
