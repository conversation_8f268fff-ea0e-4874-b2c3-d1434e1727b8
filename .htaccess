RewriteEngine On

# Przekieruj wszystkie requesty na index.php (poza plikami statycznymi)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Zabezpieczenia
<Files "config.php">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.db">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

# Cache dla plików statycznych
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
</IfModule>
